<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">

  <title>Page Not Found (404) | Siri Global Tech – Freight Forwarding Software</title>

  <meta name="description" content="The page you're looking for doesn't exist. Return to Siri Global Tech homepage for freight forwarding software solutions.">
  <meta name="robots" content="noindex, follow">
  
  <link href="https://www.siriglobaltech.in/" rel="canonical" />
  
  <!-- Critical CSS for 404 page -->
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      color: #222;
      background: linear-gradient(135deg, #33908f 0%, #2c7a79 100%);
      margin: 0;
      padding: 0;
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .error-container {
      text-align: center;
      background: #fff;
      padding: 3rem;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      max-width: 500px;
      margin: 2rem;
    }
    
    .error-code {
      font-size: 6rem;
      font-weight: bold;
      color: #33908f;
      margin: 0;
      line-height: 1;
    }
    
    .error-title {
      font-size: 2rem;
      color: #222;
      margin: 1rem 0;
    }
    
    .error-message {
      color: #666;
      margin: 1.5rem 0;
      font-size: 1.1rem;
    }
    
    .btn-home {
      display: inline-block;
      background: #FF5D20;
      color: #fff;
      padding: 12px 30px;
      text-decoration: none;
      border-radius: 30px;
      font-weight: 600;
      transition: all 0.3s ease;
      margin: 1rem 0;
    }
    
    .btn-home:hover {
      background: #e54a1a;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(229, 74, 26, 0.3);
    }
    
    .helpful-links {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #eee;
    }
    
    .helpful-links h3 {
      color: #33908f;
      margin-bottom: 1rem;
    }
    
    .helpful-links ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .helpful-links li {
      margin: 0.5rem 0;
    }
    
    .helpful-links a {
      color: #33908f;
      text-decoration: none;
      transition: color 0.3s ease;
    }
    
    .helpful-links a:hover {
      color: #FF5D20;
      text-decoration: underline;
    }
    
    @media (max-width: 768px) {
      .error-code {
        font-size: 4rem;
      }
      
      .error-title {
        font-size: 1.5rem;
      }
      
      .error-container {
        padding: 2rem;
        margin: 1rem;
      }
    }
  </style>

  <link rel="icon" type="image/x-icon" href="assets/images/faviicon/favicon.ico" sizes="48x48">
</head>

<body>
  <div class="error-container">
    <div class="error-code">404</div>
    <h1 class="error-title">Page Not Found</h1>
    <p class="error-message">
      Sorry, the page you're looking for doesn't exist or has been moved. 
      Let's get you back to our freight forwarding solutions.
    </p>
    
    <a href="/" class="btn-home">Return to Homepage</a>
    
    <div class="helpful-links">
      <h3>Popular Pages</h3>
      <ul>
        <li><a href="/#services">Our Services</a></li>
        <li><a href="/#key-features">Software Features</a></li>
        <li><a href="/#faq">Frequently Asked Questions</a></li>
        <li><a href="/#contact">Contact Us</a></li>
        <li><a href="/terms-and-conditions.html">Terms & Conditions</a></li>
      </ul>
    </div>
    
    <div style="margin-top: 2rem; color: #666; font-size: 0.9rem;">
      <p>Need help? Contact us at <a href="mailto:<EMAIL>" style="color: #33908f;"><EMAIL></a></p>
    </div>
  </div>

  <!-- Track 404 errors -->
  <script>
    // Send 404 tracking to analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'page_view', {
        page_title: '404 - Page Not Found',
        page_location: window.location.href,
        custom_map: {'custom_parameter_1': 'error_page'}
      });
    }
    
    // Log for debugging
    console.log('404 Error - Page not found:', window.location.href);
  </script>
</body>

</html>
