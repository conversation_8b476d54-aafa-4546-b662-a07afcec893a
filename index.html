<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">

  <title>Freight Forwarding Software Mumbai | Logistics ERP India</title>

  <meta name="description" content="Cloud-based freight forwarding software for logistics companies in Mumbai. Complete ERP solution with customs, tracking & invoicing. Get free demo today!">
  <meta name="keywords" content="freight forwarding software, logistics management system, shipping software, cargo management, Mumbai logistics software, freight automation, logistics software India, freight management software Mumbai, cloud freight forwarding solution, digital freight forwarding platform, shipping management software, logistics automation software, cargo tracking software India, supply chain management software, freight ERP software, customs clearance software, transport management system India, SaaS logistics software, freight billing software, logistics CRM software, multimodal logistics software">
  <meta property="og:locale" content="en_US" />
  <meta property="og:type" content="website" />
  <meta property="og:title" content="Siri Global Tech - Leading Freight Forwarding Software">
  <meta property="og:description"
    content="Transform your logistics operations with our industry-leading freight forwarding software. Get your free demo today.">
  <meta property="og:url" content="https://www.siriglobaltech.in" />
  <meta property="og:site_name" content="siriglobaltech" />

  <meta name="robots" content="index, follow">
  <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <meta name="bingbot" content="index, follow">

  <!-- Additional SEO meta tags -->
  <meta name="author" content="Siri Global Tech">
  <meta name="language" content="English">
  <meta name="geo.region" content="IN-MH">
  <meta name="geo.placename" content="Mumbai">
  <meta name="geo.position" content="19.0760;72.8777">
  <meta name="ICBM" content="19.0760, 72.8777">

  <link href="https://www.siriglobaltech.in/" rel="canonical" />

  <!-- Additional link relations for SEO -->
  <link rel="alternate" hreflang="en-IN" href="https://www.siriglobaltech.in/">
  <link rel="alternate" hreflang="en" href="https://www.siriglobaltech.in/">
  <link rel="alternate" hreflang="x-default" href="https://www.siriglobaltech.in/">
  
  <!-- Resource hints for better performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://kit.fontawesome.com">
  <link rel="dns-prefetch" href="//fonts.googleapis.com">

  <!-- Preload critical resources -->
  <link rel="preload" fetchpriority="high" as="image" href="assets/images/cloud-based-freight-software.webp" type="image/webp">

  <!-- Preload and optimize font loading -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap"></noscript>

  <!-- Preload font files for critical text -->
  <link rel="preload" href="https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2" as="font" type="font/woff2" crossorigin>

  <!-- Critical CSS - Inlined for faster rendering -->
  <style>
    /* Critical above-the-fold styles */
    :root {
      --font-default: Poppins, sans-serif;
      --font-primary: Poppins, sans-serif;
      --color-default: #222;
      --color-primary: #33908f;
      --color-white: #ffffff;
      scroll-behavior: smooth;
    }

    * {
      box-sizing: border-box;
    }

    /* Font face with font-display: swap for better performance */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: url('https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecnFHGPc.woff2') format('woff2');
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      font-display: swap;
      src: url('https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2') format('woff2');
    }

    body {
      font-family: var(--font-default);
      color: var(--color-default);
      background: #fff;
      margin: 0;
      padding: 0;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }

    .row {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -15px;
    }

    .col-lg-6 {
      flex: 0 0 50%;
      max-width: 50%;
      padding: 0 15px;
    }

    .heading {
      color: #000000;
      font-size: clamp(2rem, 5vw, 3.5rem);
      font-weight: 700;
      line-height: 1.2;
      margin: 0 0 1rem 0;
    }

    .hero {
      min-height: 100vh;
      display: flex;
      align-items: center;
      padding: 2rem 0;
    }

    .img-fluid {
      max-width: 100%;
      height: auto;
    }

    #header {
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      padding: 0.75rem 0;
      transition: all 0.3s ease;
      border-bottom: 1px solid rgba(0,0,0,0.08);
      box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    }

    .d-flex {
      display: flex;
    }

    .align-items-center {
      align-items: center;
    }

    .justify-content-between {
      justify-content: space-between;
    }

    .navbar ul {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 2rem;
    }

    .navbar a {
      color: var(--color-default);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    .main-button {
      background: #FF5D20;
      padding: 10px 20px;
      color: #fff !important;
      border-radius: 30px;
      text-decoration: none;
      transition: all 0.3s ease;
    }

    /* Dropdown menu styles */
    .dropdown {
      position: relative;
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      left: 0;
      background: #fff;
      min-width: 200px;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      border-radius: 5px;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.3s ease;
      z-index: 1000;
    }

    .dropdown:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .dropdown-menu li {
      list-style: none;
    }

    .dropdown-menu a {
      display: block;
      padding: 10px 15px;
      color: #222;
      text-decoration: none;
      border-bottom: 1px solid #f0f0f0;
      transition: background 0.3s ease;
    }

    .dropdown-menu a:hover {
      background: #f8f9fa;
      color: #33908f;
    }

    @media (max-width: 768px) {
      .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
      }

      .heading {
        font-size: 2rem;
        text-align: center;
      }

      .hero {
        text-align: center;
      }

      .navbar ul {
        display: none;
      }
    }
  </style>

  <!-- Load non-critical CSS asynchronously -->
  <link rel="preload" href="assets/stylesheets/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="assets/stylesheets/bootstrap.min.css"></noscript>

  <link rel="preload" href="assets/vendor/bootstrap-icons/bootstrap-icons.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="assets/vendor/bootstrap-icons/bootstrap-icons.css"></noscript>

  <link rel="preload" href="assets/stylesheets/styles.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="assets/stylesheets/styles.min.css"></noscript>

  <!-- Load Font Awesome asynchronously (remove duplicate CSS version) -->
  <script src="https://kit.fontawesome.com/c96def6679.js" crossorigin="anonymous" async></script>

  <!-- This script appears to be a duplicate of the one above. It can be removed. -->
  <!-- <script src="assets/javascripts/c96def6679.js" crossorigin="anonymous"></script> -->
  <!-- Consider minifying assets/vendor/bootstrap-icons/bootstrap-icons.css and assets/vendor/aos/aos.css -->
  <!-- Review Font Awesome usage: font-awesome.min.css (v4.7) might be redundant if only using the JS kit (v5/6) -->


  <link rel="icon" type="image/x-icon" href="assets/images/faviicon/favicon.ico" sizes="48x48">
  <link rel="apple-touch-icon" sizes="76x76" href="assets/images/faviicon/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="assets/images/faviicon/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="assets/images/faviicon/favicon-16x16.png">
  <link rel="manifest" href="assets/images/faviicon/site.webmanifest">
  <link rel="mask-icon" href="assets/images/faviicon/safari-pinned-tab.svg" color="#5bbad5">
  <meta name="msapplication-TileColor" content="#da532c">
  <meta name="theme-color" content="#ffffff">
 
  <!-- Comprehensive Schema Markup -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "Organization",
        "@id": "https://www.siriglobaltech.in/#organization",
        "name": "Siri Global Tech",
        "url": "https://www.siriglobaltech.in",
        "logo": {
          "@type": "ImageObject",
          "url": "https://www.siriglobaltech.in/assets/images/SIRI_LOGO.webp",
          "width": 180,
          "height": 60
        },
        "description": "Leading provider of cloud-based freight forwarding software, logistics ERP solutions, and customs automation tools for the shipping industry.",
        "foundingDate": "2020",
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "Unit No-01, Ground Floor, Plot No.60, Shri Swami Samarth CHS, Tarun Bharat Society, Chakala, Andheri East",
          "addressLocality": "Mumbai",
          "addressRegion": "Maharashtra",
          "postalCode": "400099",
          "addressCountry": "IN"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": "19.0760",
          "longitude": "72.8777"
        },
        "contactPoint": [
          {
            "@type": "ContactPoint",
            "telephone": "+91-98675-95974",
            "contactType": "Sales",
            "areaServed": "IN",
            "availableLanguage": ["English", "Hindi"]
          },
          {
            "@type": "ContactPoint",
            "telephone": "+91-90291-32115",
            "contactType": "Technical Support",
            "areaServed": "IN",
            "availableLanguage": ["English", "Hindi"]
          }
        ],
        "email": "<EMAIL>",
        "sameAs": [
          "https://www.linkedin.com/company/siriglobaltech",
          "https://twitter.com/siriglobaltech"
        ],
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Freight Forwarding Software Solutions",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "SoftwareApplication",
                "name": "Ocean Freight Management Software",
                "applicationCategory": "BusinessApplication"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "SoftwareApplication",
                "name": "Air Freight Management Software",
                "applicationCategory": "BusinessApplication"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "SoftwareApplication",
                "name": "Customs Management Software",
                "applicationCategory": "BusinessApplication"
              }
            }
          ]
        }
      },
      {
        "@type": "WebSite",
        "@id": "https://www.siriglobaltech.in/#website",
        "url": "https://www.siriglobaltech.in",
        "name": "Siri Global Tech - Freight Forwarding Software",
        "description": "Cloud-based freight forwarding software for logistics companies in Mumbai. Complete ERP solution with customs, tracking & invoicing.",
        "publisher": {
          "@id": "https://www.siriglobaltech.in/#organization"
        },
        "potentialAction": [
          {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": "https://www.siriglobaltech.in/?s={search_term_string}"
            },
            "query-input": "required name=search_term_string"
          }
        ],
        "inLanguage": "en-IN"
      },
      {
        "@type": "WebPage",
        "@id": "https://www.siriglobaltech.in/#webpage",
        "url": "https://www.siriglobaltech.in",
        "name": "Freight Forwarding Software Mumbai | Logistics ERP India",
        "isPartOf": {
          "@id": "https://www.siriglobaltech.in/#website"
        },
        "about": {
          "@id": "https://www.siriglobaltech.in/#organization"
        },
        "description": "Cloud-based freight forwarding software for logistics companies in Mumbai. Complete ERP solution with customs, tracking & invoicing. Get free demo today!",
        "breadcrumb": {
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Home",
              "item": "https://www.siriglobaltech.in/"
            }
          ]
        },
        "mainEntity": {
          "@id": "https://www.siriglobaltech.in/#organization"
        },
        "datePublished": "2024-01-01",
        "dateModified": "2025-06-25",
        "inLanguage": "en-IN"
      },
      {
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "name": "What is freight forwarding software and how does it help logistics companies?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Freight forwarding software is a cloud-based ERP solution that automates shipping operations, documentation, tracking, and customs clearance. It helps logistics companies streamline processes, reduce manual errors, and improve operational efficiency by up to 60%."
            }
          },
          {
            "@type": "Question",
            "name": "Does Siri Global Tech offer cloud-based or on-premise freight forwarding software?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "We provide 100% cloud-based freight forwarding software accessible from any device with internet connection. Our SaaS solution eliminates the need for expensive hardware, IT maintenance, and offers automatic updates with 99.9% uptime guarantee."
            }
          },
          {
            "@type": "Question",
            "name": "How can I request a free demo of your logistics ERP software?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "You can request a free personalized demo by calling +91 98675 95974, emailing <EMAIL>, or filling out our contact form. Our Mumbai-based team will schedule a 30-minute demonstration tailored to your business needs."
            }
          },
          {
            "@type": "Question",
            "name": "What types of freight does your software handle - air, sea, or land cargo?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Our comprehensive freight forwarding software handles all modes of transportation: air freight (MAWB/HAWB management), ocean freight (BOL and container tracking), and land transportation. It's an all-in-one solution for multimodal logistics operations."
            }
          },
          {
            "@type": "Question",
            "name": "Is your freight forwarding software suitable for small logistics companies in Mumbai?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes, our software is designed for logistics companies of all sizes in Mumbai and across India. We offer flexible pricing plans starting from small freight forwarders to large 3PL providers, with scalable features that grow with your business."
            }
          },
          {
            "@type": "Question",
            "name": "What kind of support do you provide?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "We offer comprehensive 24/7 technical support, dedicated account managers, regular training sessions, and a knowledge base. Our support team is available via phone, email, and live chat to assist you with any queries."
            }
          },
          {
            "@type": "Question",
            "name": "Is my data secure with your software?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes, we implement enterprise-grade security measures including data encryption, secure backups, role-based access control, and regular security audits to ensure your data is protected at all times."
            }
          },
          {
            "@type": "Question",
            "name": "Can I integrate the software with my existing systems?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes, our software offers API integration capabilities and can be integrated with various systems including ERP, CRM, accounting software, and other logistics platforms to ensure seamless data flow."
            }
          }
        ]
      }
    ]
  }
  </script>

</head>

<body>
  <div id="preloader"></div>

  <div id="top" class="animated infinite">

    <div class="top-left">
      <div class="welcome-text"><i class="fa fa-envelope" aria-hidden="true"></i> <a
          href="mailto:<EMAIL>" rel="noopener noreferrer"
          title="<EMAIL>">&nbsp;<EMAIL></a></div>
      <div class="welcome-text_mobile"><i class="fa fa-mobile-phone" aria-hidden="true"></i><a href="#"
          rel="noopener noreferrer" title="+91 **********">&nbsp;+91 98675 95974</a> </div>
      <div class="welcome-text_phone"><i class="fa fa-phone" aria-hidden="true"></i><a href="#"
          rel="noopener noreferrer" title="+91 **********">&nbsp;+91 **********</a> </div>
    </div>
    <div class="top-right">
      <div class="welcome-text main-4-section text-right">
        <ul>

          <li class="twitter-icon">
            <a href="https://twitter.com/siriglobaltech/status/942003997443088384"
              title="Siri Global Tech Follow us on Twitter" alt="Siri Global Tech Follow us on Twitter" target="_blank"
              rel="noopener noreferrer" aria-label="Follow Siri Global Tech on Twitter">
              <i class="fa fa-twitter" aria-hidden="true"></i> 
            </a>
          </li>
           
          <li class="linkin-icon"><a href="https://www.linkedin.com/company/siri-global-tech-llp"
              title="Siri Global Tech Follow us on Linkedin" target="_blank" rel="noopener noreferrer"
              aria-label="Follow Siri Global Tech on LinkedIn"><i class="fa fa-linkedin" aria-hidden="true"></i></a></li>
        </ul>

      </div>
    </div>
  </div>

  <!-- Modern Header - Redesigned -->
  <header id="modern-header" class="modern-header">
    <div class="header-container">
      <!-- Logo Section -->
      <div class="logo-section">
        <a href="https://www.siriglobaltech.in" class="logo-link">
          <img src="assets/images/SIRI_LOGO.webp"
               alt="SIRI Global Tech Logo - Freight Forwarding and Logistics Software"
               title="SIRI Global Tech - Freight Forwarding and Logistics Software (Andheri East)"
               class="logo-image"
               width="180"
               height="60">
          <div class="logo-text" style="display: none;">
            <span class="company-name">SIRI Global Tech</span>
            <span class="tagline">Freight Forwarding Solutions</span>
          </div>
        </a>
      </div>

      <!-- Navigation Section -->
      <nav class="main-navigation" id="mainNav">
        <ul class="nav-menu">
          <li class="nav-item">
            <a href="/" class="nav-link">
              <i class="fas fa-home nav-icon"></i>
              <span>Home</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#core-capabilities" class="nav-link">
              <i class="fas fa-info-circle nav-icon"></i>
              <span>About</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#smart-features" class="nav-link">
              <i class="fas fa-star nav-icon"></i>
              <span>Features</span>
            </a>
          </li>
          <li class="nav-item dropdown">
            <a href="#product-facts" class="nav-link dropdown-toggle">
              <i class="fas fa-cogs nav-icon"></i>
              <span>Services</span>
              <i class="fas fa-chevron-down dropdown-arrow"></i>
            </a>
            <ul class="dropdown-menu">
              <li><a href="/ocean-freight-software.html" class="dropdown-link">
                <i class="fas fa-ship"></i>Ocean Freight Software
              </a></li>
              <li><a href="/air-freight-software.html" class="dropdown-link">
                <i class="fas fa-plane"></i>Air Freight Software
              </a></li>
              <li><a href="/customs-management.html" class="dropdown-link">
                <i class="fas fa-file-invoice"></i>Customs Management
              </a></li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#faq" class="nav-link">
              <i class="fas fa-question-circle nav-icon"></i>
              <span>FAQ</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="/blog/" class="nav-link">
              <i class="fas fa-blog nav-icon"></i>
              <span>Blog</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#contact" class="nav-link">
              <i class="fas fa-envelope nav-icon"></i>
              <span>Contact</span>
            </a>
          </li>
        </ul>
      </nav>

      <!-- CTA Section -->
      <div class="header-cta">
        <a href="/contact.html" class="cta-button primary-cta" rel="noopener noreferrer"
           title="SIRI Global Tech - Freight Forwarding and Logistics Software Demo">
          <i class="fas fa-play-circle"></i>
          <span>Get Demo</span>
        </a>
        <a href="tel:+91**********" class="cta-button secondary-cta">
          <i class="fas fa-phone"></i>
          <span>Call Now</span>
        </a>
      </div>

      <!-- Mobile Menu Toggle -->
      <div class="mobile-menu-toggle" id="mobileToggle">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </div>
    </div>

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" id="mobileNavOverlay">
      <div class="mobile-nav-content">
        <div class="mobile-nav-header">
          <img src="assets/images/SIRI_LOGO.webp" alt="SIRI Global Tech" class="mobile-logo">
          <button class="mobile-close-btn" id="mobileCloseBtn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <ul class="mobile-nav-menu">
          <li><a href="/" class="mobile-nav-link"><i class="fas fa-home"></i>Home</a></li>
          <li><a href="#core-capabilities" class="mobile-nav-link"><i class="fas fa-info-circle"></i>About</a></li>
          <li><a href="#smart-features" class="mobile-nav-link"><i class="fas fa-star"></i>Features</a></li>
          <li><a href="#product-facts" class="mobile-nav-link"><i class="fas fa-cogs"></i>Services</a></li>
          <li><a href="#faq" class="mobile-nav-link"><i class="fas fa-question-circle"></i>FAQ</a></li>
          <li><a href="/blog/" class="mobile-nav-link"><i class="fas fa-blog"></i>Blog</a></li>
          <li><a href="#contact" class="mobile-nav-link"><i class="fas fa-envelope"></i>Contact</a></li>
        </ul>
        <div class="mobile-cta-section">
          <a href="/contact.html" class="mobile-cta-btn primary">
            <i class="fas fa-play-circle"></i>Get Free Demo
          </a>
          <a href="tel:+91**********" class="mobile-cta-btn secondary">
            <i class="fas fa-phone"></i>Call Now
          </a>
        </div>
      </div>
    </div>
  </header>
  <!-- Modern Hero/Banner Section - Redesigned -->
<main id="main">
  <section id="hero-banner" class="modern-hero-section">
    <div class="hero-background">
      <div class="hero-pattern"></div>
      <div class="hero-gradient"></div>
    </div>

    <div class="hero-container">
      <div class="hero-content" data-aos="fade-up">
        <!-- Hero Text Content -->
        <div class="hero-text-section">
          <div class="hero-badge">
            <i class="fas fa-award"></i>
            <span>Leading Freight Forwarding Software</span>
          </div>

          <h1 class="hero-title">
            Transform Your <span class="highlight-text">Freight Forwarding</span>
            <br>Operations with Smart Technology
          </h1>

          <p class="hero-description">
            Streamline your logistics operations with our comprehensive freight forwarding management software.
            From ocean and air freight to customs clearance and accounting - manage everything in one powerful platform.
          </p>

          <div class="hero-features">
            <div class="feature-item">
              <i class="fas fa-check-circle"></i>
              <span>Complete Logistics Management</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-check-circle"></i>
              <span>Real-time Tracking & Analytics</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-check-circle"></i>
              <span>Automated Compliance & Reporting</span>
            </div>
          </div>

          <div class="hero-cta-section">
            <a href="/contact.html" class="hero-cta-btn primary-btn">
              <i class="fas fa-play-circle"></i>
              <span>Get Free Demo</span>
              <div class="btn-shine"></div>
            </a>
            <a href="#core-capabilities" class="hero-cta-btn secondary-btn">
              <i class="fas fa-info-circle"></i>
              <span>Learn More</span>
            </a>
          </div>

          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">500+</div>
              <div class="stat-label">Happy Clients</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">Uptime</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">24/7</div>
              <div class="stat-label">Support</div>
            </div>
          </div>
        </div>

        <!-- Hero Image Section -->
        <div class="hero-image-section">
          <div class="hero-image-container">
            <div class="image-decoration">
              <div class="floating-element element-1">
                <i class="fas fa-ship"></i>
              </div>
              <div class="floating-element element-2">
                <i class="fas fa-plane"></i>
              </div>
              <div class="floating-element element-3">
                <i class="fas fa-chart-line"></i>
              </div>
            </div>

            <picture class="hero-main-image">
              <source media="(max-width: 480px)"
                      srcset="assets/images/cloud-based-freight-software-480.webp 480w"
                      sizes="480px">
              <source media="(max-width: 768px)"
                      srcset="assets/images/cloud-based-freight-software-768.webp 768w"
                      sizes="768px">
              <source media="(max-width: 1024px)"
                      srcset="assets/images/cloud-based-freight-software-1024.webp 1024w"
                      sizes="1024px">
              <img src="assets/images/cloud-based-freight-software.webp"
                   class="main-software-image"
                   fetchpriority="high"
                   width="600"
                   height="400"
                   alt="A diagram showing the interface of a freight forwarding management and logistics software."
                   title="Freight Forwarding Management and Logistics Software Company">
            </picture>

            <div class="image-overlay">
              <div class="play-button" id="heroPlayBtn">
                <i class="fas fa-play"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator">
      <div class="scroll-text">Scroll to explore</div>
      <div class="scroll-arrow">
        <i class="fas fa-chevron-down"></i>
      </div>
    </div>
  </section>

  <!-- Core Capabilities Section - Redesigned -->
  <section id="core-capabilities" class="core-capabilities-section">
    <div class="container">
      <div class="section-header text-center" data-aos="fade-up">
        <h2 class="capabilities-title">Our Software's Core Capabilities</h2>
        <p class="capabilities-subtitle">Comprehensive freight forwarding solutions designed for modern logistics operations</p>
      </div>

      <div class="capabilities-grid" data-aos="fade-up" data-aos-delay="200">
        <!-- Operational Features Card -->
        <div class="capability-card operational-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-cogs"></i>
            </div>
            <h3 class="card-title">Operational Features</h3>
            <p class="card-description">Complete operational management for freight forwarding</p>
          </div>
          <div class="card-content">
            <div class="feature-list">
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Customer & Vendor Master Management</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Job Creation & Rate Booking</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Document Management System</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Multi-Currency Support</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Multi-Branch Operations</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>MAWB & HAWB Generation</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>IGM File Generation</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>HBL Documentation</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Finance & Accounts Card -->
        <div class="capability-card finance-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-calculator"></i>
            </div>
            <h3 class="card-title">Finance & Accounts</h3>
            <p class="card-description">Automated financial management and accounting integration</p>
          </div>
          <div class="card-content">
            <div class="feature-list">
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Auto Job & Invoice Status Tracking</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>One-Click Invoice Generation</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Vendor Invoice Approval</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Payment & Receipt Vouchers</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Tally ERP Integration</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Cost Sheet Management</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>IRN & QR Code Generation</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Outstanding & Liability Reports</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Advanced Reports Card -->
        <div class="capability-card reports-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="card-title">Advanced Reports</h3>
            <p class="card-description">Comprehensive analytics and business intelligence</p>
          </div>
          <div class="card-content">
            <div class="feature-list">
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>MIS & Executive Reports</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Sales & Purchase Analytics</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Job Status Tracking</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Receivables & Payables Management</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Trade Payables Reports</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Financial Performance Analytics</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Integration Capabilities</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-check-circle"></i>
                <span>Custom Report Builder</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="capabilities-cta text-center" data-aos="fade-up" data-aos-delay="400">
        <h3>Ready to Transform Your Operations?</h3>
        <p>Experience the power of integrated freight forwarding software</p>
        <a href="#contact" class="btn-capabilities-demo">Get Free Demo</a>
      </div>
    </div>
  </section>
  <!-- Product Facts Section - Redesigned -->
  <section id="product-facts" class="product-facts-section">
    <div class="container">
      <!-- Section Header -->
      <div class="product-facts-header text-center" data-aos="fade-up">
        <h2 class="facts-title">Comprehensive Freight Forwarding Solutions</h2>
        <p class="facts-subtitle">
          Integrating operations in freight forwarding involves streamlining and coordinating various aspects of the
          supply chain to improve efficiency, visibility, and customer satisfaction. Our
          <a href="/ocean-freight-software.html" class="solution-link">comprehensive ocean freight management software</a>,
          <a href="/air-freight-software.html" class="solution-link">advanced air freight management system</a>, and
          <a href="/customs-management.html" class="solution-link">automated customs clearance software</a>
          work together to create a unified logistics platform.
        </p>
      </div>

      <!-- Solutions Grid -->
      <div class="solutions-grid" data-aos="fade-up" data-aos-delay="200">

        <!-- Ocean Freight Card -->
        <div class="solution-card ocean-card">
          <a href="/ocean-freight-software.html" class="card-link">
            <div class="card-icon-wrapper ocean-icon">
              <i class="fa-solid fa-ship"></i>
            </div>
            <div class="card-content">
              <h3 class="solution-title">Ocean Freight</h3>
              <p class="solution-description">
                Manage sea import/export operations efficiently with comprehensive tracking, documentation, and customs
                compliance features.
              </p>
              <div class="solution-features">
                <span class="feature-tag">Import/Export</span>
                <span class="feature-tag">Tracking</span>
                <span class="feature-tag">Compliance</span>
              </div>
            </div>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </a>
        </div>

        <!-- Air Freight Card -->
        <div class="solution-card air-card featured">
          <a href="/air-freight-software.html" class="card-link">
            <div class="card-icon-wrapper air-icon">
              <i class="fa-solid fa-plane-departure"></i>
            </div>
            <div class="card-content">
              <h3 class="solution-title">Air Freight</h3>
              <p class="solution-description">
                Efficiently handle air import/export processes with MAWB/HAWB management, tracking, and customs
                compliance functionalities.
              </p>
              <div class="solution-features">
                <span class="feature-tag">MAWB/HAWB</span>
                <span class="feature-tag">Real-time</span>
                <span class="feature-tag">Customs</span>
              </div>
            </div>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </a>
        </div>

        <!-- Customs Management Card -->
        <div class="solution-card customs-card">
          <a href="/customs-management.html" class="card-link">
            <div class="card-icon-wrapper customs-icon">
              <i class="fa-solid fa-file-invoice"></i>
            </div>
            <div class="card-content">
              <h3 class="solution-title">Customs Management</h3>
              <p class="solution-description">
                Automate customs clearance processes with documentation, duty calculations, and compliance
                reporting for import/export operations.
              </p>
              <div class="solution-features">
                <span class="feature-tag">Automation</span>
                <span class="feature-tag">Calculations</span>
                <span class="feature-tag">Reporting</span>
              </div>
            </div>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </a>
        </div>

        <!-- Accounting Card -->
        <div class="solution-card accounting-card">
          <div class="card-icon-wrapper accounting-icon">
            <i class="fa-solid fa-calculator"></i>
          </div>
          <div class="card-content">
            <h3 class="solution-title">Accounting</h3>
            <p class="solution-description">
              Manage financial transactions and reporting with integrated accounting functionalities, ensuring accuracy
              and compliance in freight forwarding operations.
            </p>
            <div class="solution-features">
              <span class="feature-tag">Financial</span>
              <span class="feature-tag">Reporting</span>
              <span class="feature-tag">Compliance</span>
            </div>
          </div>
          <div class="card-arrow">
            <i class="fas fa-cog"></i>
          </div>
        </div>

        <!-- E-Invoicing Card -->
        <div class="solution-card invoicing-card">
          <div class="card-icon-wrapper invoicing-icon">
            <i class="fa-solid fa-receipt"></i>
          </div>
          <div class="card-content">
            <h3 class="solution-title">E-Invoicing</h3>
            <p class="solution-description">
              Automate invoicing processes through electronic means, ensuring efficiency and accuracy in transaction
              documentation within freight forwarding operations.
            </p>
            <div class="solution-features">
              <span class="feature-tag">Electronic</span>
              <span class="feature-tag">Automated</span>
              <span class="feature-tag">Accurate</span>
            </div>
          </div>
          <div class="card-arrow">
            <i class="fas fa-bolt"></i>
          </div>
        </div>
      </div>

      <!-- Stats Section -->
      <div class="product-stats" data-aos="fade-up" data-aos-delay="400">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">500+</div>
            <div class="stat-label">Active Clients</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">99.9%</div>
            <div class="stat-label">Uptime</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">24/7</div>
            <div class="stat-label">Support</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">50+</div>
            <div class="stat-label">Countries</div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Smart Features Section - Redesigned -->
  <section id="smart-features" class="smart-features-section">
    <div class="container">
      <!-- Section Header -->
      <div class="smart-features-header text-center" data-aos="fade-up">
        <h2 class="smart-features-title">Smart Features to Empower Your Business</h2>
        <p class="smart-features-subtitle">
          Our Freight Forwarding Software is an Exclusively Complete Logistics Solution for your Organization.
          Learn more about <a href="/blog/" class="insights-link">freight forwarding insights and best practices</a>
          in our comprehensive resource center.
        </p>
      </div>

      <!-- Main Content Grid -->
      <div class="smart-features-grid" data-aos="fade-up" data-aos-delay="200">

        <!-- Key Features Card -->
        <div class="features-card key-features-card">
          <div class="card-header-smart">
            <div class="header-icon">
              <i class="fas fa-star"></i>
            </div>
            <h3 class="card-title-smart">Key Features</h3>
            <p class="card-subtitle-smart">Comprehensive tools for modern logistics</p>
          </div>
          <div class="features-list-container">
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Order Management</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Multi-Branch Management</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Air Way Bill Print</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Document Management</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Report Customizations</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Receivables & Payables Management</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Costing & Revenue Management</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Multi-Modal Freight Management</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>Workflow & Task Automation</span>
            </div>
            <div class="feature-item-smart">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>One-Click Invoice Generation</span>
            </div>
          </div>
        </div>

        <!-- Central Image -->
        <div class="features-image-container">
          <div class="image-wrapper">
            <img class="features-main-image"
                 alt="A diagram showing the key features of the freight forwarding software."
                 src="assets/images/key_features.webp"
                 width="400"
                 height="300"
                 loading="lazy">
            <div class="image-overlay">
              <div class="overlay-content">
                <i class="fas fa-play-circle play-icon"></i>
                <span>See Features in Action</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Benefits Card -->
        <div class="features-card benefits-card">
          <div class="card-header-smart benefits-header">
            <div class="header-icon">
              <i class="fas fa-rocket"></i>
            </div>
            <h3 class="card-title-smart">We Make a Difference</h3>
            <p class="card-subtitle-smart">Streamline and automate freight shipment processes</p>
          </div>

          <div class="benefits-list">
            <div class="benefit-item">
              <div class="benefit-icon-wrapper">
                <img src="assets/images/kf_icon1.webp"
                     alt="Integrate Operations"
                     class="benefit-icon"
                     width="60"
                     height="60"
                     loading="lazy">
              </div>
              <div class="benefit-content">
                <h4 class="benefit-title">Integrate Operations</h4>
                <p class="benefit-description">Seamless coordination across all freight forwarding operations</p>
              </div>
            </div>

            <div class="benefit-item">
              <div class="benefit-icon-wrapper">
                <img src="assets/images/kf_icon2.webp"
                     alt="Increase Productivity"
                     class="benefit-icon"
                     width="60"
                     height="60"
                     loading="lazy">
              </div>
              <div class="benefit-content">
                <h4 class="benefit-title">Increase Productivity</h4>
                <p class="benefit-description">Leverage automation and technology for streamlined processes</p>
              </div>
            </div>

            <div class="benefit-item">
              <div class="benefit-icon-wrapper">
                <img src="assets/images/kf_icon3.webp"
                     alt="Better Customer Service"
                     class="benefit-icon"
                     width="60"
                     height="60"
                     loading="lazy">
              </div>
              <div class="benefit-content">
                <h4 class="benefit-title">Better Customer Service</h4>
                <p class="benefit-description">Seamless and satisfying experience throughout shipping</p>
              </div>
            </div>

            <div class="benefit-item">
              <div class="benefit-icon-wrapper">
                <img src="assets/images/kf_icon4.webp"
                     alt="Increase Profitability"
                     class="benefit-icon"
                     width="60"
                     height="60"
                     loading="lazy">
              </div>
              <div class="benefit-content">
                <h4 class="benefit-title">Increase Profitability</h4>
                <p class="benefit-description">Optimize routes, reduce costs, and identify revenue opportunities</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Call to Action -->
      
    </div>
  </section>
  

  <section id="software-features" class="section software-features section-padding">

    <div class="section-header">
      <h2>Services Features to save your time and help you</h2>
      <!-- Removed duplicate section class -->
      <h4 class="service_features">Save time and stay financially organized with automated invoicing and expense
        tracking tailored for your freight forwarding business.</h4>
    </div>

    <div class="container">
      <div class="price-plan-wrapper">
        <div class="row">
          <div class="col-lg-4 col-md-6 image-padding-center">
            <div><img class="img-fluid mobile_img"
                alt="Siri Global Tech Freight Forwarding Features"
                src="assets/images/mobile_on_page.webp"
                width="300"
                height="600"
                style="aspect-ratio: 1/2; object-fit: contain;"
                loading="lazy"></div>
          </div>

          <div class="col-lg-4  col-md-4">
            <!-- Removed inline styles for color and padding, using classes or existing styles -->
            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Easy Invoices</span><br />Create
                      professional looking customised invoices within seconds with your company</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">

                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Master BL</span><br />Master
                      Bill of Lading as per norms</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Fast Integrated
                        Payment</span><br />Streamline Transactions Seamlessly Integrate and Accelerate Payments</p>
                  </div>
                </div>
              </div>
            </div>



            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Auto E-Invoice
                        Positing</span><br />Simplify Invoicing Automate Posting with Our E-Invoice Solution</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Reduces costs</span><br />While
                      negotiating favorable rates with carriers and suppliers to maximize savings without compromising
                      service quality</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Advance
                        Reporting</span><br />Elevate Insights Unlock Advanced MIS / Ledger Reporting Capabilities</p>
                  </div>
                </div>
              </div>
            </div>

          </div>

          <div class="col-lg-4  col-md-4">

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span
                        style="font-size: 18px; font-weight: bold; color:#000000 ;">Multi-Currency</span><br />Effortlessly
                      Manage Multi-Currency Transactions</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">House BL</span><br />House BL as
                      per custom requirement</p>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Job
                        Profitability</span><br />Get cost, income and profit of each job</p>
                  </div>
                </div>
              </div>
            </div>



            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Cloud Storage</span><br />Access
                      scalable and secure cloud storage solutions for convenient and reliable data management in your
                      freight forwarding operations</p>
                  </div>
                </div>
              </div>
            </div>


            <div class="col-lg-12">
              <div class="stats-item d-flex align-items-left" style="margin-bottom: 5px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <p style="text-align: left; color: #000000; padding: 0 5px; font-weight: normal; font-size: 14px;">
                      <span style="font-size: 18px; font-weight: bold; color:#000000 ;">Auto Payment
                        Reminder</span><br />Stay on Track Effortlessly Send Auto Payment Reminders for Timely
                      Settlements</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section id="faq" class="faq-section">
    <div class="container">
      <div class="section-header text-center mb-5">
        <h2>Frequently Asked Questions</h2>
        <p class="lead">Find answers to common questions about our freight forwarding software</p>
      </div>
  
      <div class="faq-container">
        <div class="faq-item">
          <div class="faq-question">
            <h3>What kind of support do you provide?</h3>
            <span class="faq-icon">+</span>
          </div>
          <div class="faq-answer">
            <p>We offer comprehensive 24/7 technical support, dedicated account managers, regular training sessions, and a knowledge base. Our support team is available via phone, email, and live chat to assist you with any queries.</p>
          </div>
        </div>
        
        <div class="faq-item">
          <div class="faq-question">
            <h3>Is my data secure with your software?</h3>
            <span class="faq-icon">+</span>
          </div>
          <div class="faq-answer">
            <p>Yes, we implement enterprise-grade security measures including data encryption, secure backups, role-based access control, and regular security audits to ensure your data is protected at all times.</p>
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            <h3>Can I integrate the software with my existing systems?</h3>
            <span class="faq-icon">+</span>
          </div>
          <div class="faq-answer">
            <p>Yes, our software offers API integration capabilities and can be integrated with various systems including ERP, CRM, accounting software, and other logistics platforms to ensure seamless data flow.</p>
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            <h3>What are the payment terms and pricing options?</h3>
            <span class="faq-icon">+</span>
          </div>
          <div class="faq-answer">
            <p>We offer flexible subscription plans based on your business size and requirements. Contact our sales team for a customized quote. We provide monthly and annual billing options with special discounts for long-term commitments.</p>
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            <h3>Can I access the software on mobile devices?</h3>
            <span class="faq-icon">+</span>
          </div>
          <div class="faq-answer">
            <p>Yes, our software is fully responsive and can be accessed on any mobile device. We also offer dedicated mobile apps for both iOS and Android platforms for enhanced mobility.</p>
          </div>
        </div>

        <div class="faq-item">
          <div class="faq-question">
            <h3>What training resources are available?</h3>
            <span class="faq-icon">+</span>
          </div>
          <div class="faq-answer">
            <p>We provide comprehensive training resources including live training sessions, video tutorials, user guides, documentation, and regular webinars to help you maximize the software's potential.</p>
          </div>
        </div>
        
    <style>
      .faq-section {
        padding: 80px 0;
        background: #f8f9fa;
      }
  
      .faq-container {
        max-width: 800px;
        margin: 0 auto;
      }
  
      .faq-item {
        background: #fff;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }
  
      .faq-question {
        padding: 20px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
  
      .faq-question h3 {
        margin: 0;
        font-size: 1.1rem;
        color: #333;
      }
  
      .faq-icon {
        font-size: 1.5rem;
        color: #0d6efd;
        transition: transform 0.3s ease;
      }
  
      .faq-answer {
        max-height: 0;
        overflow: hidden;
        padding: 0 20px;
        color: #666;
        line-height: 1.6;
        transition: max-height 0.3s ease, padding 0.3s ease;
      }

      .faq-item.active .faq-icon {
        transform: rotate(45deg);
      }

      .faq-item.active .faq-answer {
        max-height: 200px; /* Approximate content height to prevent layout shift */
        padding: 0 20px 20px;
      }
    </style>
  
    <!-- FAQ functionality moved to external performance-optimized.js -->
  </section>

    <!-- Contact Us Section - Redesigned -->
    <section id="contact" class="modern-contact-section">
      <div class="container">
        <!-- Section Header -->
        <div class="contact-header text-center" data-aos="fade-up">
          <h2 class="contact-title">Get in Touch</h2>
          <p class="contact-subtitle">
            Have a question about our software solutions? Interested in partnering with us? Or maybe you just want to say hello?
            We'd love to hear from you! <a href="/contact.html" class="demo-link">Schedule a personalized demo</a>
            to see how our freight forwarding software can transform your logistics operations.
          </p>
        </div>

        <!-- Contact Content Grid -->
        <div class="contact-grid" data-aos="fade-up" data-aos-delay="200">

          <!-- Contact Form Card -->
          <div class="contact-form-card">
            <div class="form-header">
              <h3>Send us a Message</h3>
              <p>Fill out the form below and we'll get back to you within 24 hours</p>
            </div>

            <form id="contactForm" class="modern-contact-form" action="enquiry_process.php" method="post">
              <div class="form-grid">
                <!-- Name Field -->
                <div class="form-group">
                  <label for="name" class="form-label">
                    <i class="fas fa-user"></i>
                    Full Name
                  </label>
                  <input type="text" name="name" id="name" class="form-input" required
                    data-error="Please enter your name" placeholder="Enter your full name" autocomplete="name">
                </div>

                <!-- Company Field -->
                <div class="form-group">
                  <label for="companyname" class="form-label">
                    <i class="fas fa-building"></i>
                    Company Name
                  </label>
                  <input type="text" name="companyname" id="companyname" class="form-input" required
                    data-error="Please enter your Company Name" placeholder="Enter your company name" autocomplete="organization">
                </div>

                <!-- Email Field -->
                <div class="form-group">
                  <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i>
                    Email Address
                  </label>
                  <input type="email" name="email" id="email" class="form-input" required
                    data-error="Please enter your email" placeholder="Enter your email address" autocomplete="email">
                </div>

                <!-- Phone Field -->
                <div class="form-group">
                  <label for="phone" class="form-label">
                    <i class="fas fa-phone"></i>
                    Phone Number
                  </label>
                  <input type="text" name="phone" id="phone" class="form-input" required
                    data-error="Please enter your Phone No" placeholder="Enter your phone number" autocomplete="tel">
                </div>

                <!-- Employees Field -->
                <div class="form-group">
                  <label for="numemployees" class="form-label">
                    <i class="fas fa-users"></i>
                    Company Size
                  </label>
                  <select id="numemployees" name="numemployees" class="form-select" required>
                    <option value="" disabled selected>Select number of employees</option>
                    <option value="1-5">1-5 employees</option>
                    <option value="5-25">5-25 employees</option>
                    <option value="25-50">25-50 employees</option>
                    <option value="50-100">50-100 employees</option>
                    <option value="100-500">100-500 employees</option>
                    <option value="500-1000">500-1000 employees</option>
                    <option value="1000+">1000+ employees</option>
                  </select>
                </div>

                <!-- Designation Field -->
                <div class="form-group">
                  <label for="designation-2" class="form-label">
                    <i class="fas fa-briefcase"></i>
                    Your Role
                  </label>
                  <select id="designation-2" name="designation" class="form-select" required>
                    <option value="" disabled selected>Select your designation</option>
                    <option value="CEO">CEO</option>
                    <option value="Operations Manager">Operations Manager</option>
                    <option value="Sales Manager">Sales Manager</option>
                    <option value="Operation Executive">Operation Executive</option>
                    <option value="Accounting Personnel">Accounting Personnel</option>
                    <option value="IT Manager">IT Manager</option>
                  </select>
                </div>

                <!-- Message Field -->
                <div class="form-group form-group-full">
                  <label for="message" class="form-label">
                    <i class="fas fa-comment"></i>
                    Message
                  </label>
                  <textarea name="message" id="message" rows="5" class="form-textarea" required
                    data-error="Please enter your message" placeholder="Tell us about your requirements..."></textarea>
                </div>

                <!-- Submit Button -->
                <div class="form-group form-group-full">
                  <input type="hidden" name="access" id="access" value="yes" />
                  <button type="submit" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    Send Message
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- Contact Information Card -->
          <div class="contact-info-card">
            <div class="info-header">
              <h3>Contact Information</h3>
              <p>Reach out to us directly through any of these channels</p>
            </div>

            <div class="contact-methods">
              <!-- Address -->
              <div class="contact-method">
                <div class="method-icon address-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="method-content">
                  <h4>Our Office</h4>
                  <p>
                    Unit No-01, Ground Floor<br>
                    Plot No.60, Shri Swami Samarth CHS<br>
                    Tarun Bharat Society, Chakala<br>
                    Andheri East, Mumbai<br>
                    Maharashtra 400099, India
                  </p>
                </div>
              </div>

              <!-- Phone -->
              <div class="contact-method">
                <div class="method-icon phone-icon">
                  <i class="fas fa-phone-alt"></i>
                </div>
                <div class="method-content">
                  <h4>Call Us</h4>
                  <p>
                    <strong>Sales:</strong> <a href="tel:+91**********">+91 98675 95974</a> (Brijesh)<br>
                    <strong>Support:</strong> <a href="tel:+91**********">+91 90291 32115</a> (Abhishek)
                  </p>
                </div>
              </div>

              <!-- Email -->
              <div class="contact-method">
                <div class="method-icon email-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="method-content">
                  <h4>Email Us</h4>
                  <p>
                    <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    <span class="response-time">We respond within 24 hours</span>
                  </p>
                </div>
              </div>

              <!-- Business Hours -->
              <div class="contact-method">
                <div class="method-icon hours-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="method-content">
                  <h4>Business Hours</h4>
                  <p>
                    <strong>Monday - Friday:</strong> 9:00 AM - 6:00 PM<br>
                    <strong>Saturday:</strong> 10:00 AM - 4:00 PM<br>
                    <strong>Sunday:</strong> Closed
                  </p>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
              <h4>Quick Actions</h4>
              <div class="action-buttons">
                <a href="/contact.html" class="action-btn demo-btn">
                  <i class="fas fa-play"></i>
                  Schedule Demo
                </a>
                <a href="tel:+91**********" class="action-btn call-btn">
                  <i class="fas fa-phone"></i>
                  Call Now
                </a>
                <a href="mailto:<EMAIL>" class="action-btn email-btn">
                  <i class="fas fa-envelope"></i>
                  Send Email
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
  
  <!-- Modern Footer - Redesigned -->
  <footer class="modern-footer">
    <div class="footer-main">
      <div class="footer-container">
        <!-- Footer Content Grid -->
        <div class="footer-grid">

          <!-- Company Info Section -->
          <div class="footer-section company-info">
            <div class="footer-logo">
              <img src="assets/images/SIRI_LOGO.webp" alt="SIRI Global Tech" class="footer-logo-img">
              <div class="footer-logo-text">
                <h3>SIRI Global Tech</h3>
                <p>Freight Forwarding Solutions</p>
              </div>
            </div>
            <p class="company-description">
              Leading provider of comprehensive freight forwarding and logistics software solutions.
              Streamline your operations with our cutting-edge technology platform.
            </p>
            <div class="social-links">
              <a href="#" class="social-link" aria-label="Facebook">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="social-link" aria-label="Twitter">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="social-link" aria-label="LinkedIn">
                <i class="fab fa-linkedin-in"></i>
              </a>
              <a href="#" class="social-link" aria-label="Instagram">
                <i class="fab fa-instagram"></i>
              </a>
            </div>
          </div>

          <!-- Quick Links Section -->
          <div class="footer-section quick-links">
            <h4 class="footer-title">Quick Links</h4>
            <ul class="footer-links">
              <li><a href="/"><i class="fas fa-home"></i>Home</a></li>
              <li><a href="#core-capabilities"><i class="fas fa-info-circle"></i>About Us</a></li>
              <li><a href="#smart-features"><i class="fas fa-star"></i>Features</a></li>
              <li><a href="#product-facts"><i class="fas fa-cogs"></i>Services</a></li>
              <li><a href="#faq"><i class="fas fa-question-circle"></i>FAQ</a></li>
              <li><a href="/blog/"><i class="fas fa-blog"></i>Blog</a></li>
            </ul>
          </div>

          <!-- Services Section -->
          <div class="footer-section services-links">
            <h4 class="footer-title">Our Services</h4>
            <ul class="footer-links">
              <li><a href="/ocean-freight-software.html"><i class="fas fa-ship"></i>Ocean Freight Software</a></li>
              <li><a href="/air-freight-software.html"><i class="fas fa-plane"></i>Air Freight Software</a></li>
              <li><a href="/customs-management.html"><i class="fas fa-file-invoice"></i>Customs Management</a></li>
              <li><a href="#"><i class="fas fa-calculator"></i>Accounting Solutions</a></li>
              <li><a href="#"><i class="fas fa-receipt"></i>E-Invoicing</a></li>
              <li><a href="#"><i class="fas fa-chart-line"></i>Analytics & Reports</a></li>
            </ul>
          </div>

          <!-- Contact Info Section -->
          <div class="footer-section contact-info">
            <h4 class="footer-title">Contact Information</h4>
            <div class="contact-details">
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <div class="contact-text">
                  <strong>Address:</strong><br>
                  Unit No-01, Ground Floor<br>
                  Plot No.60, Shri Swami Samarth CHS<br>
                  Andheri East, Mumbai 400099
                </div>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <div class="contact-text">
                  <strong>Phone:</strong><br>
                  <a href="tel:+91**********">+91 98675 95974</a> (Sales)<br>
                  <a href="tel:+91**********">+91 90291 32115</a> (Support)
                </div>
              </div>
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <div class="contact-text">
                  <strong>Email:</strong><br>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Bottom -->
    <div class="footer-bottom">
      <div class="footer-container">
        <div class="footer-bottom-content">
          <div class="copyright">
            <p>&copy; 2025 <strong>SIRI Global Tech</strong>. All Rights Reserved.</p>
          </div>
          <div class="footer-bottom-links">
            <a href="terms-and-conditions.html">Terms & Conditions</a>
            <span class="separator">|</span>
            <a href="privacy-policy.html">Privacy Policy</a>
            <span class="separator">|</span>
            <a href="#contact">Contact Us</a>
          </div>
          <div class="footer-badges">
            <div class="badge-item">
              <i class="fas fa-shield-alt"></i>
              <span>Secure</span>
            </div>
            <div class="badge-item">
              <i class="fas fa-clock"></i>
              <span>24/7 Support</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <a href="#" class="scroll-top d-flex align-items-center justify-content-center"><i
      class="bi bi-arrow-up-short"></i></a>
  <!-- Defer all scripts to prevent them from blocking page rendering -->
  <script src="assets/javascripts/jquery.min.js" defer></script>
  <script src="assets/javascripts/bootstrap.min.js" defer></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js" defer></script>
  <script src="assets/vendor/aos/aos.js" defer></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js" defer></script>
  <!-- <script src="assets/javascripts/plugins.js"></script> -->
  <script src="assets/javascripts/validator.min.js" defer></script>
  <script src="assets/javascripts/contactform.js" defer></script>

  <script src="assets/javascripts/main.js" defer></script>
  <script src="assets/javascripts/performance-optimized.js" defer></script>
  <script src="assets/javascripts/web-vitals.js" defer></script>

  <!-- CSS-based text animation for better performance -->
  <style>
    .word-container {
      height: 1.2em; /* Set a fixed height to prevent layout shift */
    }
    .word {
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      border-right: .1em solid #FFD12A; /* Blinking cursor */
      animation:
        typing 2s steps(30, end) forwards,
        blink-caret .75s step-end infinite,
        change-word 16s linear infinite;
      animation-delay: 0s, 0s, 2s;
      max-width: 0;
    }

    .word:before {
      content: 'That Supports Your Success';
    }

    @keyframes typing {
      from { max-width: 0 }
      to { max-width: 100% }
    }

    @keyframes blink-caret {
      from, to { border-color: transparent }
      50% { border-color: #FFD12A; }
    }

    @keyframes change-word {
      0%, 20% { content: 'That Supports Your Success'; }
      25%, 45% { content: 'Built to increase profits'; }
      50%, 70% { content: 'streamline day-to-day operations'; }
      75%, 95% { content: 'and support your customers.'; }
      100% { content: 'That Supports Your Success'; }
    }
  </style>

  <!-- Core Capabilities Section Styles -->
  <style>
    .core-capabilities-section {
      background: linear-gradient(135deg, #e0eeff1c 0%, #764ba2 100%);
      padding: 80px 0;
      position: relative;
      overflow: hidden;
    }

    .core-capabilities-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .capabilities-title {
      color: #ffffff;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .capabilities-subtitle {
      color: rgba(255,255,255,0.9);
      font-size: 1.2rem;
      margin-bottom: 3rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .capabilities-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
      margin-bottom: 3rem;
      position: relative;
      z-index: 2;
    }

    .capability-card {
      background: rgba(255,255,255,0.95);
      border-radius: 16px;
      padding: 0;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
      overflow: hidden;
      backdrop-filter: blur(10px);
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .capability-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    }

    .card-header {
      padding: 2rem 2rem 1rem;
      text-align: center;
      position: relative;
    }

    .operational-card .card-header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .finance-card .card-header {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .reports-card .card-header {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .card-icon {
      width: 80px;
      height: 80px;
      background: rgba(255,255,255,0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      backdrop-filter: blur(10px);
    }

    .card-icon i {
      font-size: 2rem;
      color: #ffffff;
    }

    .card-title {
      color: #ffffff;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .card-description {
      color: rgba(255,255,255,0.9);
      font-size: 0.95rem;
      margin-bottom: 0;
    }

    .card-content {
      padding: 1.5rem 2rem 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .feature-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      flex: 1;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      transition: all 0.2s ease;
    }

    .feature-item:last-child {
      border-bottom: none;
    }

    .feature-item:hover {
      background: rgba(0,0,0,0.02);
      padding-left: 0.5rem;
      border-radius: 6px;
    }

    .feature-item i {
      color: #28a745;
      font-size: 0.9rem;
      flex-shrink: 0;
    }

    .feature-item span {
      color: #333;
      font-size: 0.9rem;
      line-height: 1.4;
    }

    .capabilities-cta {
      background: rgba(255,255,255,0.1);
      border-radius: 16px;
      padding: 2.5rem 2rem;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255,255,255,0.2);
      position: relative;
      z-index: 2;
    }

    .capabilities-cta h3 {
      color: #ffffff;
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .capabilities-cta p {
      color: rgba(255,255,255,0.9);
      font-size: 1.1rem;
      margin-bottom: 1.5rem;
    }

    .btn-capabilities-demo {
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      color: #333;
      padding: 1rem 2.5rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      display: inline-block;
      box-shadow: 0 4px 15px rgba(255,209,42,0.4);
    }

    .btn-capabilities-demo:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255,209,42,0.6);
      color: #333;
      text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .capabilities-grid {
        gap: 1rem;
      }

      .card-header {
        padding: 1.5rem 1.5rem 1rem;
      }

      .card-content {
        padding: 1rem 1.5rem 1.5rem;
      }

      .feature-item span {
        font-size: 0.85rem;
      }
    }

    @media (max-width: 992px) {
      .capabilities-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .capabilities-title {
        font-size: 2rem;
      }

      .capabilities-subtitle {
        font-size: 1rem;
      }

      .capabilities-cta {
        padding: 2rem 1.5rem;
      }
    }

    @media (max-width: 768px) {
      .core-capabilities-section {
        padding: 60px 0;
      }

      .capabilities-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .capability-card {
        border-radius: 12px;
      }

      .card-icon {
        width: 60px;
        height: 60px;
      }

      .card-icon i {
        font-size: 1.5rem;
      }

      .card-title {
        font-size: 1.3rem;
      }

      .feature-item {
        gap: 0.5rem;
      }

      .feature-item span {
        font-size: 0.8rem;
      }
    }

    @media (max-width: 480px) {
      .core-capabilities-section {
        padding: 50px 0;
      }

      .card-header {
        padding: 1rem 1rem 0.5rem;
      }

      .card-content {
        padding: 0.5rem 1rem 1rem;
      }

      .card-icon {
        width: 50px;
        height: 50px;
      }

      .card-icon i {
        font-size: 1.2rem;
      }

      .card-title {
        font-size: 1.1rem;
      }

      .card-description {
        font-size: 0.8rem;
      }

      .feature-item span {
        font-size: 0.75rem;
      }

      .capabilities-cta {
        padding: 1.5rem 1rem;
      }

      .capabilities-cta h3 {
        font-size: 1.4rem;
      }

      .capabilities-cta p {
        font-size: 0.9rem;
      }
    }
  </style>

  <!-- Smart Features Section Styles -->
  <style>
    .smart-features-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 80px 0;
      position: relative;
      overflow: hidden;
    }

    .smart-features-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,0,0,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
      opacity: 0.5;
    }

    .smart-features-header {
      margin-bottom: 4rem;
      position: relative;
      z-index: 2;
    }

    .smart-features-title {
      color: #2c3e50;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      position: relative;
    }

    .smart-features-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      border-radius: 2px;
    }

    .smart-features-subtitle {
      color: #6c757d;
      font-size: 1.1rem;
      line-height: 1.6;
      max-width: 700px;
      margin: 0 auto;
    }

    .insights-link {
      color: #2a5298;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .insights-link:hover {
      color: #1e3d72;
      text-decoration: underline;
    }

    .smart-features-grid {
      display: grid;
      grid-template-columns: 1fr 400px 1fr;
      gap: 2rem;
      margin-bottom: 4rem;
      position: relative;
      z-index: 2;
      align-items: start;
    }

    .features-card {
      background: #ffffff;
      border-radius: 20px;
      padding: 0;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      overflow: hidden;
      height: fit-content;
    }

    .features-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .card-header-smart {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 2rem;
      text-align: center;
      color: white;
      position: relative;
    }

    .benefits-header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .header-icon {
      width: 60px;
      height: 60px;
      background: rgba(255,255,255,0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      backdrop-filter: blur(10px);
    }

    .header-icon i {
      font-size: 1.5rem;
      color: #ffffff;
    }

    .card-title-smart {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #ffffff;
    }

    .card-subtitle-smart {
      font-size: 0.95rem;
      color: rgba(255,255,255,0.9);
      margin: 0;
    }

    .features-list-container {
      padding: 2rem;
    }

    .feature-item-smart {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f8f9fa;
      transition: all 0.2s ease;
    }

    .feature-item-smart:last-child {
      border-bottom: none;
    }

    .feature-item-smart:hover {
      background: #f8f9fa;
      padding-left: 1rem;
      border-radius: 8px;
    }

    .feature-icon {
      color: #28a745;
      font-size: 1rem;
      flex-shrink: 0;
    }

    .feature-item-smart span {
      color: #495057;
      font-size: 0.95rem;
      font-weight: 500;
    }

    /* Central Image Styling */
    .features-image-container {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .image-wrapper {
      position: relative;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 15px 35px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    }

    .image-wrapper:hover {
      transform: scale(1.05);
      box-shadow: 0 20px 45px rgba(0,0,0,0.3);
    }

    .features-main-image {
      width: 100%;
      height: auto;
      display: block;
      /* aspect-ratio: 4/3; */
      object-fit: cover;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102,126,234,0.8) 0%, rgba(118,75,162,0.8) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: all 0.3s ease;
    }

    .image-wrapper:hover .image-overlay {
      opacity: 1;
    }

    .overlay-content {
      text-align: center;
      color: white;
    }

    .play-icon {
      font-size: 3rem;
      margin-bottom: 0.5rem;
      display: block;
    }

    .overlay-content span {
      font-size: 1.1rem;
      font-weight: 600;
    }

    /* Benefits Card Styling */
    .benefits-list {
      padding: 2rem;
    }

    .benefit-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem 0;
      border-bottom: 1px solid #f8f9fa;
      transition: all 0.3s ease;
    }

    .benefit-item:last-child {
      border-bottom: none;
    }

    .benefit-item:hover {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 12px;
      margin: 0 -1rem;
    }

    .benefit-icon-wrapper {
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
    }

    .benefit-icon {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 8px;
    }

    .benefit-content {
      flex: 1;
    }

    .benefit-title {
      color: #2c3e50;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .benefit-description {
      color: #6c757d;
      font-size: 0.9rem;
      line-height: 1.5;
      margin: 0;
    }

    /* Call to Action */
    .smart-features-cta {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 3rem 2rem;
      color: white;
      position: relative;
      z-index: 2;
    }

    .smart-features-cta h3 {
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    .smart-features-cta p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn-smart-demo, .btn-smart-features {
      padding: 1rem 2rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1rem;
      transition: all 0.3s ease;
      display: inline-block;
    }

    .btn-smart-demo {
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      color: #333;
      box-shadow: 0 4px 15px rgba(255,209,42,0.4);
    }

    .btn-smart-demo:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255,209,42,0.6);
      color: #333;
      text-decoration: none;
    }

    .btn-smart-features {
      background: transparent;
      color: white;
      border: 2px solid rgba(255,255,255,0.3);
    }

    .btn-smart-features:hover {
      background: rgba(255,255,255,0.1);
      border-color: rgba(255,255,255,0.5);
      color: white;
      text-decoration: none;
      transform: translateY(-2px);
    }
    /* Responsive Design for Smart Features */
    @media (max-width: 1200px) {
      .smart-features-grid {
        grid-template-columns: 1fr 350px 1fr;
        gap: 1.5rem;
      }
    }

    @media (max-width: 992px) {
      .smart-features-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .features-image-container {
        order: -1;
      }

      .smart-features-title {
        font-size: 2rem;
      }

      .card-header-smart {
        padding: 1.5rem;
      }

      .features-list-container, .benefits-list {
        padding: 1.5rem;
      }
    }

    @media (max-width: 768px) {
      .smart-features-section {
        padding: 60px 0;
      }

      .smart-features-header {
        margin-bottom: 3rem;
      }

      .smart-features-title {
        font-size: 1.8rem;
      }

      .smart-features-subtitle {
        font-size: 1rem;
      }

      .features-card {
        border-radius: 16px;
      }

      .card-header-smart {
        padding: 1.5rem 1rem;
      }

      .header-icon {
        width: 50px;
        height: 50px;
      }

      .header-icon i {
        font-size: 1.2rem;
      }

      .card-title-smart {
        font-size: 1.3rem;
      }

      .features-list-container, .benefits-list {
        padding: 1rem;
      }

      .benefit-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
      }

      .benefit-icon-wrapper {
        align-self: center;
      }

      .smart-features-cta {
        padding: 2rem 1rem;
      }

      .smart-features-cta h3 {
        font-size: 1.5rem;
      }

      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }

      .btn-smart-demo, .btn-smart-features {
        width: 100%;
        max-width: 280px;
      }
    }

    @media (max-width: 480px) {
      .smart-features-section {
        padding: 50px 0;
      }

      .smart-features-title {
        font-size: 1.6rem;
      }

      .card-header-smart {
        padding: 1rem;
      }

      .features-list-container, .benefits-list {
        padding: 0.75rem;
      }

      .feature-item-smart, .benefit-item {
        padding: 0.5rem 0;
      }

      .feature-item-smart span {
        font-size: 0.85rem;
      }

      .benefit-title {
        font-size: 1rem;
      }

      .benefit-description {
        font-size: 0.85rem;
      }

      .smart-features-cta {
        padding: 1.5rem 0.75rem;
      }

      .smart-features-cta h3 {
        font-size: 1.3rem;
      }

      .smart-features-cta p {
        font-size: 1rem;
      }
    }
  </style>

  <!-- Product Facts Section Styles -->
  <style>
    .product-facts-section {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      padding: 80px 0;
      position: relative;
      overflow: hidden;
    }

    .product-facts-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="50" height="43.4" patternUnits="userSpaceOnUse"><polygon points="25,0 50,14.4 50,28.9 25,43.4 0,28.9 0,14.4" fill="none" stroke="rgba(0,0,0,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
      opacity: 0.5;
    }

    .product-facts-header {
      margin-bottom: 4rem;
      position: relative;
      z-index: 2;
    }

    .facts-title {
      color: #2c3e50;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      position: relative;
    }

    .facts-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
    }

    .facts-subtitle {
      color: #6c757d;
      font-size: 1.1rem;
      line-height: 1.7;
      max-width: 800px;
      margin: 0 auto;
    }

    .solution-link {
      color: #2a5298;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      border-bottom: 1px solid transparent;
    }

    .solution-link:hover {
      color: #1e3d72;
      border-bottom-color: #1e3d72;
      text-decoration: none;
    }

    .solutions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 4rem;
      position: relative;
      z-index: 2;
    }

    .solution-card {
      background: #ffffff;
      border-radius: 20px;
      padding: 0;
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
      transition: all 0.4s ease;
      overflow: hidden;
      position: relative;
      border: 2px solid transparent;
    }

    .solution-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .solution-card.featured {
      border-color: #FFD12A;
      transform: scale(1.02);
    }

    .solution-card.featured::before {
      content: 'POPULAR';
      position: absolute;
      top: 15px;
      right: -25px;
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      color: #333;
      padding: 5px 30px;
      font-size: 0.75rem;
      font-weight: 700;
      transform: rotate(45deg);
      z-index: 3;
    }

    .card-link {
      display: block;
      text-decoration: none;
      color: inherit;
      height: 100%;
      padding: 2rem;
      position: relative;
    }

    .card-link:hover {
      text-decoration: none;
      color: inherit;
    }

    .card-icon-wrapper {
      width: 80px;
      height: 80px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      position: relative;
      transition: all 0.3s ease;
    }

    .ocean-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .air-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .customs-icon {
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .accounting-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .invoicing-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .card-icon-wrapper i {
      font-size: 2rem;
      color: #ffffff;
      transition: all 0.3s ease;
    }

    .solution-card:hover .card-icon-wrapper {
      transform: scale(1.1) rotate(5deg);
    }

    .card-content {
      flex: 1;
    }

    .solution-title {
      color: #2c3e50;
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1rem;
      transition: all 0.3s ease;
    }

    .solution-card:hover .solution-title {
      color: #667eea;
    }

    .solution-description {
      color: #6c757d;
      font-size: 0.95rem;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .solution-features {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .feature-tag {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      color: #495057;
      padding: 0.25rem 0.75rem;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .solution-card:hover .feature-tag {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #ffffff;
    }

    .card-arrow {
      position: absolute;
      bottom: 1.5rem;
      right: 1.5rem;
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transform: translateX(20px);
      transition: all 0.3s ease;
    }

    .card-arrow i {
      color: #ffffff;
      font-size: 1rem;
    }

    .solution-card:hover .card-arrow {
      opacity: 1;
      transform: translateX(0);
    }

    /* Stats Section */
    .product-stats {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 3rem 2rem;
      position: relative;
      z-index: 2;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 2rem;
      text-align: center;
    }

    .stat-item {
      color: #ffffff;
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stat-label {
      font-size: 1rem;
      font-weight: 500;
      opacity: 0.9;
    }
    /* Responsive Design for Product Facts */
    @media (max-width: 1200px) {
      .solutions-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
      }
    }

    @media (max-width: 992px) {
      .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
      }

      .facts-title {
        font-size: 2rem;
      }

      .card-link {
        padding: 1.5rem;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
      }
    }

    @media (max-width: 768px) {
      .product-facts-section {
        padding: 60px 0;
      }

      .product-facts-header {
        margin-bottom: 3rem;
      }

      .facts-title {
        font-size: 1.8rem;
      }

      .facts-subtitle {
        font-size: 1rem;
      }

      .solutions-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .solution-card {
        border-radius: 16px;
      }

      .solution-card.featured {
        transform: none;
      }

      .card-link {
        padding: 1.5rem;
      }

      .card-icon-wrapper {
        width: 60px;
        height: 60px;
        border-radius: 15px;
      }

      .card-icon-wrapper i {
        font-size: 1.5rem;
      }

      .solution-title {
        font-size: 1.2rem;
      }

      .solution-description {
        font-size: 0.9rem;
      }

      .product-stats {
        padding: 2rem 1rem;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      .stat-number {
        font-size: 2rem;
      }
    }

    @media (max-width: 480px) {
      .product-facts-section {
        padding: 50px 0;
      }

      .facts-title {
        font-size: 1.6rem;
      }

      .card-link {
        padding: 1rem;
      }

      .card-icon-wrapper {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        margin-bottom: 1rem;
      }

      .card-icon-wrapper i {
        font-size: 1.2rem;
      }

      .solution-title {
        font-size: 1.1rem;
      }

      .solution-description {
        font-size: 0.85rem;
        margin-bottom: 1rem;
      }

      .feature-tag {
        font-size: 0.75rem;
        padding: 0.2rem 0.6rem;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .stat-number {
        font-size: 1.8rem;
      }

      .stat-label {
        font-size: 0.9rem;
      }

      .product-stats {
        padding: 1.5rem 1rem;
        border-radius: 16px;
      }
    }
  </style>

  <!-- Modern Contact Section Styles - Cross-Browser Optimized -->
  <style>
    .modern-contact-section {
      background: -webkit-linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      background: -moz-linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      padding: 80px 0;
      position: relative;
      overflow: hidden;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .modern-contact-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="contact-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="rgba(102,126,234,0.05)"/><circle cx="10" cy="10" r="1" fill="rgba(102,126,234,0.03)"/><circle cx="30" cy="30" r="1" fill="rgba(102,126,234,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23contact-pattern)"/></svg>');
      opacity: 0.6;
      pointer-events: none;
    }

    .contact-header {
      margin-bottom: 4rem;
      position: relative;
      z-index: 2;
    }

    .contact-title {
      color: #2c3e50;
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      position: relative;
      text-rendering: optimizeLegibility;
    }

    .contact-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      -webkit-transform: translateX(-50%);
      -moz-transform: translateX(-50%);
      -ms-transform: translateX(-50%);
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 2px;
    }

    .contact-subtitle {
      color: #6c757d;
      font-size: 1.1rem;
      line-height: 1.7;
      max-width: 700px;
      margin: 0 auto;
      text-rendering: optimizeLegibility;
    }

    .demo-link {
      color: #2a5298;
      text-decoration: none;
      font-weight: 600;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      -ms-transition: all 0.3s ease;
      transition: all 0.3s ease;
      border-bottom: 1px solid transparent;
    }

    .demo-link:hover {
      color: #1e3d72;
      border-bottom-color: #1e3d72;
      text-decoration: none;
    }

    .contact-grid {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      flex-wrap: wrap;
      gap: 3rem;
      position: relative;
      z-index: 2;
    }

    /* Fallback for older browsers */
    @supports (display: grid) {
      .contact-grid {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 3rem;
      }
    }

    .contact-form-card {
      -webkit-flex: 1;
      -moz-flex: 1;
      -ms-flex: 1;
      flex: 1;
      min-width: 300px;
    }

    .contact-info-card {
      -webkit-flex: 0 0 400px;
      -moz-flex: 0 0 400px;
      -ms-flex: 0 0 400px;
      flex: 0 0 400px;
      min-width: 300px;
    }

    .contact-form-card, .contact-info-card {
      background: #ffffff;
      border-radius: 20px;
      padding: 0;
      -webkit-box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      -moz-box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
      overflow: hidden;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }

    .form-header, .info-header {
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 2rem;
      color: white;
      text-align: center;
    }

    .form-header h3, .info-header h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      text-rendering: optimizeLegibility;
    }

    .form-header p, .info-header p {
      font-size: 0.95rem;
      opacity: 0.9;
      margin: 0;
    }

    .modern-contact-form {
      padding: 2rem;
    }

    .form-grid {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin: -0.75rem;
    }

    /* Grid fallback for modern browsers */
    @supports (display: grid) {
      .form-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        margin: 0;
      }
    }

    .form-group {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-flex: 1;
      -moz-flex: 1;
      -ms-flex: 1;
      flex: 1;
      min-width: calc(50% - 1.5rem);
      margin: 0.75rem;
    }

    @supports (display: grid) {
      .form-group {
        margin: 0;
        min-width: auto;
      }
    }

    .form-group-full {
      -webkit-flex: 1 1 100%;
      -moz-flex: 1 1 100%;
      -ms-flex: 1 1 100%;
      flex: 1 1 100%;
      min-width: calc(100% - 1.5rem);
    }

    @supports (display: grid) {
      .form-group-full {
        grid-column: 1 / -1;
        min-width: auto;
      }
    }

    .form-label {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      -ms-align-items: center;
      align-items: center;
      gap: 0.5rem;
      color: #495057;
      font-weight: 600;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }

    .form-label i {
      color: #667eea;
      font-size: 0.9rem;
    }

    .form-input, .form-select, .form-textarea {
      padding: 1rem;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      font-size: 1rem;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      -ms-transition: all 0.3s ease;
      transition: all 0.3s ease;
      background: #ffffff;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      font-family: inherit;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
      outline: none;
      border-color: #667eea;
      -webkit-box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
      -moz-box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
      box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
    }

    .form-select {
      background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23667eea" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
      background-repeat: no-repeat;
      background-position: right 1rem center;
      background-size: 1rem;
      padding-right: 3rem;
    }

    .form-textarea {
      resize: vertical;
      min-height: 120px;
      font-family: inherit;
    }

    .submit-btn {
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 1rem 2rem;
      border-radius: 50px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      -ms-transition: all 0.3s ease;
      transition: all 0.3s ease;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      -ms-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      -ms-justify-content: center;
      justify-content: center;
      gap: 0.5rem;
      width: 100%;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }

    .submit-btn:hover {
      -webkit-transform: translateY(-2px);
      -moz-transform: translateY(-2px);
      -ms-transform: translateY(-2px);
      transform: translateY(-2px);
      -webkit-box-shadow: 0 8px 25px rgba(102,126,234,0.3);
      -moz-box-shadow: 0 8px 25px rgba(102,126,234,0.3);
      box-shadow: 0 8px 25px rgba(102,126,234,0.3);
    }

    /* Contact Information Card - Cross-Browser Optimized */
    .contact-methods {
      padding: 2rem;
    }

    .contact-method {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: flex-start;
      -moz-align-items: flex-start;
      -ms-align-items: flex-start;
      align-items: flex-start;
      gap: 1rem;
      padding: 1.5rem 0;
      border-bottom: 1px solid #f8f9fa;
    }

    .contact-method:last-child {
      border-bottom: none;
    }

    .method-icon {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      -ms-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      -ms-justify-content: center;
      justify-content: center;
      -webkit-flex-shrink: 0;
      -moz-flex-shrink: 0;
      -ms-flex-shrink: 0;
      flex-shrink: 0;
    }

    .address-icon {
      background: -webkit-linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      background: -moz-linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .phone-icon {
      background: -webkit-linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      background: -moz-linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    .email-icon {
      background: -webkit-linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      background: -moz-linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .hours-icon {
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .method-icon i {
      color: white;
      font-size: 1.2rem;
    }

    .method-content {
      -webkit-flex: 1;
      -moz-flex: 1;
      -ms-flex: 1;
      flex: 1;
    }

    .method-content h4 {
      color: #2c3e50;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      text-rendering: optimizeLegibility;
    }

    .method-content p {
      color: #6c757d;
      font-size: 0.9rem;
      line-height: 1.5;
      margin: 0;
    }

    .method-content a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      -ms-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .method-content a:hover {
      color: #5a67d8;
      text-decoration: underline;
    }

    .response-time {
      font-size: 0.8rem;
      color: #28a745;
      font-weight: 500;
    }

    /* Quick Actions - Cross-Browser Optimized */
    .quick-actions {
      padding: 0 2rem 2rem;
      border-top: 1px solid #f8f9fa;
      margin-top: 1rem;
      padding-top: 2rem;
    }

    .quick-actions h4 {
      color: #2c3e50;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      text-align: center;
      text-rendering: optimizeLegibility;
    }

    .action-buttons {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      -ms-flex-direction: column;
      flex-direction: column;
      gap: 0.75rem;
    }

    .action-btn {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: -webkit-flex;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      -ms-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      -ms-justify-content: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      border-radius: 10px;
      text-decoration: none;
      font-weight: 500;
      font-size: 0.9rem;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      -ms-transition: all 0.3s ease;
      transition: all 0.3s ease;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }

    .demo-btn {
      background: -webkit-linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      background: -moz-linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      background: linear-gradient(135deg, #FFD12A 0%, #FF8C00 100%);
      color: #333;
    }

    .call-btn {
      background: -webkit-linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      background: -moz-linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      color: white;
    }

    .email-btn {
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .action-btn:hover {
      -webkit-transform: translateY(-2px);
      -moz-transform: translateY(-2px);
      -ms-transform: translateY(-2px);
      transform: translateY(-2px);
      -webkit-box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      -moz-box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      text-decoration: none;
      color: inherit;
    }
    /* Responsive Design for Contact Section - Cross-Browser */
    @media (max-width: 1200px) {
      @supports (display: grid) {
        .contact-grid {
          grid-template-columns: 1fr 350px;
          gap: 2rem;
        }
      }

      .contact-info-card {
        -webkit-flex: 0 0 350px;
        -moz-flex: 0 0 350px;
        -ms-flex: 0 0 350px;
        flex: 0 0 350px;
      }
    }

    @media (max-width: 992px) {
      @supports (display: grid) {
        .contact-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }
      }

      .contact-grid {
        -webkit-flex-direction: column;
        -moz-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 2rem;
      }

      .contact-form-card, .contact-info-card {
        -webkit-flex: 1;
        -moz-flex: 1;
        -ms-flex: 1;
        flex: 1;
        min-width: 100%;
      }

      .contact-title {
        font-size: 2rem;
      }

      .form-grid {
        gap: 1rem;
      }

      .form-group {
        min-width: calc(50% - 1rem);
        margin: 0.5rem;
      }

      @supports (display: grid) {
        .form-group {
          margin: 0;
          min-width: auto;
        }
      }

      .modern-contact-form {
        padding: 1.5rem;
      }

      .contact-methods {
        padding: 1.5rem;
      }

      .quick-actions {
        padding: 0 1.5rem 1.5rem;
      }
    }

    @media (max-width: 768px) {
      .modern-contact-section {
        padding: 60px 0;
      }

      .contact-header {
        margin-bottom: 3rem;
      }

      .contact-title {
        font-size: 1.8rem;
      }

      .contact-subtitle {
        font-size: 1rem;
      }

      .contact-form-card, .contact-info-card {
        border-radius: 16px;
      }

      .form-header, .info-header {
        padding: 1.5rem;
      }

      @supports (display: grid) {
        .form-grid {
          grid-template-columns: 1fr;
          gap: 1rem;
        }
      }

      .form-grid {
        -webkit-flex-direction: column;
        -moz-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        gap: 1rem;
      }

      .form-group {
        min-width: 100%;
        margin: 0;
      }

      .modern-contact-form {
        padding: 1rem;
      }

      .contact-methods {
        padding: 1rem;
      }

      .contact-method {
        padding: 1rem 0;
      }

      .method-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
      }

      .method-icon i {
        font-size: 1rem;
      }

      .method-content h4 {
        font-size: 1rem;
      }

      .method-content p {
        font-size: 0.85rem;
      }

      .quick-actions {
        padding: 0 1rem 1rem;
        padding-top: 1rem;
      }

      .action-buttons {
        gap: 0.5rem;
      }

      .action-btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
      }
    }

    @media (max-width: 480px) {
      .modern-contact-section {
        padding: 50px 0;
      }

      .contact-title {
        font-size: 1.6rem;
      }

      .form-header, .info-header {
        padding: 1rem;
      }

      .form-header h3, .info-header h3 {
        font-size: 1.3rem;
      }

      .form-header p, .info-header p {
        font-size: 0.9rem;
      }

      .modern-contact-form {
        padding: 0.75rem;
      }

      .form-input, .form-select, .form-textarea {
        padding: 0.75rem;
        font-size: 0.9rem;
      }

      .form-label {
        font-size: 0.85rem;
      }

      .submit-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
      }

      .contact-methods {
        padding: 0.75rem;
      }

      .contact-method {
        -webkit-flex-direction: column;
        -moz-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
        padding: 0.75rem 0;
      }

      .method-icon {
        -webkit-align-self: center;
        -moz-align-self: center;
        -ms-align-self: center;
        align-self: center;
        width: 35px;
        height: 35px;
      }

      .method-content h4 {
        font-size: 0.95rem;
      }

      .method-content p {
        font-size: 0.8rem;
      }

      .quick-actions {
        padding: 0 0.75rem 0.75rem;
      }

      .quick-actions h4 {
        font-size: 1rem;
      }

      .action-btn {
        padding: 0.5rem 0.6rem;
        font-size: 0.8rem;
      }
    }

    /* Safari-specific optimizations */
    @supports (-webkit-appearance: none) {
      .form-input, .form-select, .form-textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        -webkit-border-radius: 12px;
        border-radius: 12px;
      }

      .submit-btn, .action-btn {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        -webkit-border-radius: 50px;
        border-radius: 50px;
      }

      .action-btn:not(.demo-btn) {
        -webkit-border-radius: 10px;
        border-radius: 10px;
      }
    }

    /* Chrome-specific optimizations */
    @supports (background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%)) {
      .form-input:focus, .form-select:focus, .form-textarea:focus {
        -webkit-box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
        -moz-box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
        box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
      }
    }

    /* Performance optimizations for all browsers */
    .modern-contact-section * {
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
    }

    .contact-form-card, .contact-info-card, .submit-btn, .action-btn {
      will-change: transform;
    }

    /* Prevent text selection on interactive elements */
    .submit-btn, .action-btn {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  </style>

  <!-- Modern Header, Hero Banner & Footer Styles -->
  <style>
    /* Modern Header Styles - Cross-Browser Optimized */
    .modern-header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(0,0,0,0.1);
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      padding: 0.75rem 0;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .header-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: space-between;
      -moz-justify-content: space-between;
      justify-content: space-between;
    }

    .logo-section {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
    }

    .logo-link {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 1rem;
      text-decoration: none;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .logo-image {
      width: 160px;
      height: auto;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .logo-text {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      flex-direction: column;
    }

    .company-name {
      font-size: 1.2rem;
      font-weight: 700;
      color: #2c3e50;
      line-height: 1.2;
    }

    .tagline {
      font-size: 0.8rem;
      color: #667eea;
      font-weight: 500;
    }

    .main-navigation {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
    }

    .nav-menu {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 2rem;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
    }

    .nav-item {
      position: relative;
    }

    .nav-link {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.5rem;
      color: #2c3e50;
      text-decoration: none;
      font-weight: 500;
      font-size: 0.95rem;
      padding: 0.5rem 1rem;
      border-radius: 8px;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      position: relative;
    }

    .nav-link:hover {
      color: #667eea;
      background: rgba(102,126,234,0.1);
      text-decoration: none;
    }

    .nav-icon {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .dropdown-arrow {
      font-size: 0.7rem;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .dropdown:hover .dropdown-arrow {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      transform: rotate(180deg);
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      left: 0;
      background: #ffffff;
      min-width: 250px;
      -webkit-box-shadow: 0 10px 30px rgba(0,0,0,0.15);
      -moz-box-shadow: 0 10px 30px rgba(0,0,0,0.15);
      box-shadow: 0 10px 30px rgba(0,0,0,0.15);
      border-radius: 12px;
      opacity: 0;
      visibility: hidden;
      -webkit-transform: translateY(-10px);
      -moz-transform: translateY(-10px);
      transform: translateY(-10px);
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      z-index: 1000;
      overflow: hidden;
      border: 1px solid rgba(0,0,0,0.1);
    }

    .dropdown:hover .dropdown-menu {
      opacity: 1;
      visibility: visible;
      -webkit-transform: translateY(0);
      -moz-transform: translateY(0);
      transform: translateY(0);
    }

    .dropdown-menu li {
      list-style: none;
    }

    .dropdown-link {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 1.5rem;
      color: #2c3e50;
      text-decoration: none;
      font-weight: 500;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .dropdown-link:hover {
      background: #f8f9fa;
      color: #667eea;
      text-decoration: none;
    }

    .dropdown-link:last-child {
      border-bottom: none;
    }

    .header-cta {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      gap: 1rem;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
    }

    .cta-button {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      font-size: 0.9rem;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }

    .primary-cta {
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .primary-cta:hover {
      -webkit-transform: translateY(-2px);
      -moz-transform: translateY(-2px);
      transform: translateY(-2px);
      -webkit-box-shadow: 0 8px 25px rgba(102,126,234,0.3);
      -moz-box-shadow: 0 8px 25px rgba(102,126,234,0.3);
      box-shadow: 0 8px 25px rgba(102,126,234,0.3);
      color: white;
      text-decoration: none;
    }

    .secondary-cta {
      background: transparent;
      color: #667eea;
      border-color: #667eea;
    }

    .secondary-cta:hover {
      background: #667eea;
      color: white;
      text-decoration: none;
    }

    .mobile-menu-toggle {
      display: none;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      flex-direction: column;
      gap: 4px;
      cursor: pointer;
      padding: 0.5rem;
    }

    .hamburger-line {
      width: 25px;
      height: 3px;
      background: #2c3e50;
      border-radius: 2px;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }
    /* Modern Hero Banner Styles */
    .modern-hero-section {
      min-height: 100vh;
      position: relative;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      padding-top: 120px;
      padding-bottom: 60px;
      overflow: hidden;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
    }

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }

    .hero-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(102,126,234,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
      opacity: 0.6;
    }

    .hero-gradient {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: -webkit-linear-gradient(135deg, rgba(102,126,234,0.05) 0%, rgba(255,255,255,0.95) 100%);
      background: -moz-linear-gradient(135deg, rgba(102,126,234,0.05) 0%, rgba(255,255,255,0.95) 100%);
      background: linear-gradient(135deg, rgba(102,126,234,0.05) 0%, rgba(255,255,255,0.95) 100%);
    }

    .hero-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
      position: relative;
      z-index: 2;
    }

    .hero-content {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 4rem;
      min-height: 80vh;
    }

    .hero-text-section {
      -webkit-flex: 1;
      -moz-flex: 1;
      flex: 1;
      max-width: 600px;
    }

    .hero-badge {
      display: -webkit-inline-box;
      display: -moz-inline-box;
      display: -ms-inline-flexbox;
      display: inline-flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.5rem;
      background: rgba(99,102,241,0.1);
      color: #6366f1;
      padding: 0.75rem 1.25rem;
      border-radius: 30px;
      font-size: 0.95rem;
      font-weight: 600;
      margin-bottom: 2rem;
      border: 1px solid rgba(99,102,241,0.2);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .hero-badge:hover {
      background: rgba(99,102,241,0.15);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(99,102,241,0.15);
    }

    .hero-title {
      font-size: 4rem;
      font-weight: 900;
      color: #1a202c;
      line-height: 1.15;
      margin-bottom: 2rem;
      text-rendering: optimizeLegibility;
      letter-spacing: -0.02em;
    }

    .highlight-text {
      background: -webkit-linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
      background: -moz-linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
    }

    .highlight-text::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      border-radius: 2px;
      opacity: 0.4;
    }

    .hero-description {
      font-size: 1.25rem;
      color: #4a5568;
      line-height: 1.7;
      margin-bottom: 2.5rem;
      font-weight: 400;
      max-width: 90%;
    }

    .hero-features {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      flex-direction: column;
      gap: 1rem;
      margin-bottom: 2.5rem;
    }

    .feature-item {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 1rem;
      color: #2d3748;
      font-weight: 600;
      font-size: 1.05rem;
      padding: 0.5rem 0;
      transition: all 0.3s ease;
    }

    .feature-item:hover {
      transform: translateX(8px);
      color: #1a202c;
    }

    .feature-item i {
      color: #10b981;
      font-size: 1.2rem;
      background: rgba(16, 185, 129, 0.1);
      padding: 8px;
      border-radius: 50%;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .feature-item:hover i {
      background: rgba(16, 185, 129, 0.2);
      transform: scale(1.1);
    }

    .hero-cta-section {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      gap: 1.5rem;
      margin-bottom: 3rem;
      -webkit-flex-wrap: wrap;
      -moz-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    .hero-cta-btn {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 2rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .primary-btn {
      background: -webkit-linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      background: -moz-linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      color: white;
      -webkit-box-shadow: 0 10px 30px rgba(99,102,241,0.3);
      -moz-box-shadow: 0 10px 30px rgba(99,102,241,0.3);
      box-shadow: 0 10px 30px rgba(99,102,241,0.3);
      border: none;
    }

    .primary-btn:hover {
      -webkit-transform: translateY(-4px) scale(1.02);
      -moz-transform: translateY(-4px) scale(1.02);
      transform: translateY(-4px) scale(1.02);
      -webkit-box-shadow: 0 20px 40px rgba(99,102,241,0.4);
      -moz-box-shadow: 0 20px 40px rgba(99,102,241,0.4);
      box-shadow: 0 20px 40px rgba(99,102,241,0.4);
      color: white;
      text-decoration: none;
      background: -webkit-linear-gradient(135deg, #5b5ff1 0%, #7c3aed 100%);
      background: -moz-linear-gradient(135deg, #5b5ff1 0%, #7c3aed 100%);
      background: linear-gradient(135deg, #5b5ff1 0%, #7c3aed 100%);
    }

    .secondary-btn {
      background: rgba(99,102,241,0.05);
      color: #6366f1;
      border: 2px solid rgba(99,102,241,0.2);
      backdrop-filter: blur(10px);
    }

    .secondary-btn:hover {
      background: rgba(99,102,241,0.1);
      color: #5b5ff1;
      border-color: #6366f1;
      text-decoration: none;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(99,102,241,0.15);
    }

    .btn-shine {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: -webkit-linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      background: -moz-linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      -webkit-transition: left 0.6s ease;
      -moz-transition: left 0.6s ease;
      transition: left 0.6s ease;
    }

    .primary-btn:hover .btn-shine {
      left: 100%;
    }

    .hero-stats {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      gap: 2rem;
      -webkit-flex-wrap: wrap;
      -moz-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    .stat-item {
      text-align: center;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #6c757d;
      font-weight: 500;
    }

    .hero-image-section {
      -webkit-flex: 1;
      -moz-flex: 1;
      flex: 1;
      position: relative;
    }

    .hero-image-container {
      position: relative;
      max-width: 600px;
      margin: 0 auto;
    }

    .image-decoration {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
    }

    .floating-element {
      position: absolute;
      width: 70px;
      height: 70px;
      background: -webkit-linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
      background: -moz-linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
      border-radius: 50%;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      justify-content: center;
      color: white;
      font-size: 1.6rem;
      -webkit-animation: float 4s ease-in-out infinite;
      -moz-animation: float 4s ease-in-out infinite;
      animation: float 4s ease-in-out infinite;
      box-shadow: 0 8px 25px rgba(99,102,241,0.3);
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255,255,255,0.2);
    }

    .element-1 {
      top: 10%;
      right: 10%;
      -webkit-animation-delay: 0s;
      -moz-animation-delay: 0s;
      animation-delay: 0s;
    }

    .element-2 {
      top: 60%;
      left: 5%;
      -webkit-animation-delay: 1s;
      -moz-animation-delay: 1s;
      animation-delay: 1s;
    }

    .element-3 {
      bottom: 15%;
      right: 15%;
      -webkit-animation-delay: 2s;
      -moz-animation-delay: 2s;
      animation-delay: 2s;
    }

    @-webkit-keyframes float {
      0%, 100% { -webkit-transform: translateY(0px); }
      50% { -webkit-transform: translateY(-20px); }
    }

    @-moz-keyframes float {
      0%, 100% { -moz-transform: translateY(0px); }
      50% { -moz-transform: translateY(-20px); }
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    .hero-main-image {
      position: relative;
      z-index: 2;
    }

    .main-software-image {
      width: 100%;
      height: auto;
      border-radius: 24px;
      -webkit-box-shadow: 0 25px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(99,102,241,0.1);
      -moz-box-shadow: 0 25px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(99,102,241,0.1);
      box-shadow: 0 25px 60px rgba(0,0,0,0.15), 0 8px 25px rgba(99,102,241,0.1);
      transition: all 0.4s ease;
      border: 1px solid rgba(255,255,255,0.2);
    }

    .main-software-image:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 35px 80px rgba(0,0,0,0.2), 0 15px 35px rgba(99,102,241,0.15);
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(102,126,234,0.1);
      border-radius: 20px;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      justify-content: center;
      opacity: 0;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      z-index: 3;
    }

    .hero-image-container:hover .image-overlay {
      opacity: 1;
    }

    .play-button {
      width: 80px;
      height: 80px;
      background: rgba(255,255,255,0.9);
      border-radius: 50%;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      justify-content: center;
      color: #667eea;
      font-size: 2rem;
      cursor: pointer;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .play-button:hover {
      -webkit-transform: scale(1.1);
      -moz-transform: scale(1.1);
      transform: scale(1.1);
    }

    .scroll-indicator {
      position: absolute;
      bottom: 2rem;
      left: 50%;
      -webkit-transform: translateX(-50%);
      -moz-transform: translateX(-50%);
      transform: translateX(-50%);
      text-align: center;
      z-index: 2;
    }

    .scroll-text {
      font-size: 0.9rem;
      color: #6c757d;
      margin-bottom: 0.5rem;
    }

    .scroll-arrow {
      color: #667eea;
      font-size: 1.5rem;
      -webkit-animation: bounce 2s infinite;
      -moz-animation: bounce 2s infinite;
      animation: bounce 2s infinite;
    }

    @-webkit-keyframes bounce {
      0%, 20%, 50%, 80%, 100% { -webkit-transform: translateY(0); }
      40% { -webkit-transform: translateY(-10px); }
      60% { -webkit-transform: translateY(-5px); }
    }

    @-moz-keyframes bounce {
      0%, 20%, 50%, 80%, 100% { -moz-transform: translateY(0); }
      40% { -moz-transform: translateY(-10px); }
      60% { -moz-transform: translateY(-5px); }
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      60% { transform: translateY(-5px); }
    }
    /* Modern Footer Styles */
    .modern-footer {
      background: -webkit-linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
      background: -moz-linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
      background: linear-gradient(135deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
      color: #ffffff;
      position: relative;
      overflow: hidden;
    }

    .modern-footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23footer-pattern)"/></svg>');
      opacity: 0.3;
    }

    .footer-main {
      padding: 4rem 0 2rem;
      position: relative;
      z-index: 2;
    }

    .footer-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .footer-grid {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-wrap: wrap;
      -moz-flex-wrap: wrap;
      flex-wrap: wrap;
      gap: 3rem;
    }

    @supports (display: grid) {
      .footer-grid {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1.5fr;
        gap: 3rem;
      }
    }

    .footer-section {
      -webkit-flex: 1;
      -moz-flex: 1;
      flex: 1;
      min-width: 250px;
    }

    .company-info {
      -webkit-flex: 2;
      -moz-flex: 2;
      flex: 2;
    }

    .footer-logo {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .footer-logo-img {
      width: 120px;
      height: auto;
    }

    .footer-logo-text h3 {
      color: #ffffff;
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
    }

    .footer-logo-text p {
      color: rgba(255,255,255,0.7);
      font-size: 0.9rem;
      margin: 0;
    }

    .company-description {
      color: rgba(255,255,255,0.8);
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    .social-links {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      gap: 1rem;
    }

    .social-link {
      width: 40px;
      height: 40px;
      background: rgba(255,255,255,0.1);
      border-radius: 50%;
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      justify-content: center;
      color: #ffffff;
      text-decoration: none;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .social-link:hover {
      background: #667eea;
      -webkit-transform: translateY(-2px);
      -moz-transform: translateY(-2px);
      transform: translateY(-2px);
      color: #ffffff;
      text-decoration: none;
    }

    .footer-title {
      color: #ffffff;
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      position: relative;
    }

    .footer-title::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 40px;
      height: 3px;
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      border-radius: 2px;
    }

    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .footer-links li {
      margin-bottom: 0.75rem;
    }

    .footer-links a {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.5rem;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      font-size: 0.95rem;
    }

    .footer-links a:hover {
      color: #8b5cf6;
      -webkit-transform: translateX(8px);
      -moz-transform: translateX(8px);
      transform: translateX(8px);
      text-decoration: none;
    }

    .footer-links i {
      font-size: 0.8rem;
      opacity: 0.7;
    }

    .contact-details {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      flex-direction: column;
      gap: 1.5rem;
    }

    .contact-item {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: flex-start;
      -moz-align-items: flex-start;
      align-items: flex-start;
      gap: 1rem;
    }

    .contact-item i {
      color: #667eea;
      font-size: 1.2rem;
      margin-top: 0.2rem;
      -webkit-flex-shrink: 0;
      -moz-flex-shrink: 0;
      flex-shrink: 0;
    }

    .contact-text {
      color: rgba(255,255,255,0.8);
      line-height: 1.5;
      font-size: 0.95rem;
    }

    .contact-text strong {
      color: #ffffff;
      display: block;
      margin-bottom: 0.25rem;
    }

    .contact-text a {
      color: #667eea;
      text-decoration: none;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .contact-text a:hover {
      color: #5a67d8;
      text-decoration: underline;
    }

    .footer-bottom {
      background: rgba(0,0,0,0.2);
      padding: 1.5rem 0;
      border-top: 1px solid rgba(255,255,255,0.1);
      position: relative;
      z-index: 2;
    }

    .footer-bottom-content {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: space-between;
      -moz-justify-content: space-between;
      justify-content: space-between;
      -webkit-flex-wrap: wrap;
      -moz-flex-wrap: wrap;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .copyright p {
      color: rgba(255,255,255,0.8);
      margin: 0;
      font-size: 0.9rem;
    }

    .copyright strong {
      color: #ffffff;
    }

    .footer-bottom-links {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 1rem;
      -webkit-flex-wrap: wrap;
      -moz-flex-wrap: wrap;
      flex-wrap: wrap;
    }

    .footer-bottom-links a {
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      font-size: 0.9rem;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .footer-bottom-links a:hover {
      color: #8b5cf6;
      text-decoration: none;
    }

    .separator {
      color: rgba(255,255,255,0.5);
    }

    .footer-badges {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      gap: 1rem;
    }

    .badge-item {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 0.5rem;
      color: rgba(255,255,255,0.8);
      font-size: 0.85rem;
    }

    .badge-item i {
      color: #28a745;
      font-size: 0.9rem;
    }

    /* Responsive Footer Styles */
    @media (max-width: 992px) {
      .footer-grid {
        -webkit-flex-direction: column;
        -moz-flex-direction: column;
        flex-direction: column;
        gap: 2rem;
      }

      @supports (display: grid) {
        .footer-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }
      }

      .footer-main {
        padding: 3rem 0 1.5rem;
      }
    }

    @media (max-width: 768px) {
      .footer-container {
        padding: 0 1rem;
      }

      .footer-bottom-content {
        -webkit-flex-direction: column;
        -moz-flex-direction: column;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }

      .footer-badges {
        -webkit-justify-content: center;
        -moz-justify-content: center;
        justify-content: center;
      }
    }

    /* Mobile Navigation Styles */
    .mobile-nav-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0,0,0,0.9);
      z-index: 9999;
      opacity: 0;
      visibility: hidden;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .mobile-nav-overlay.active {
      opacity: 1;
      visibility: visible;
    }

    .mobile-nav-content {
      background: #ffffff;
      width: 90%;
      max-width: 400px;
      height: 100vh;
      position: absolute;
      right: 0;
      top: 0;
      padding: 2rem;
      -webkit-transform: translateX(100%);
      -moz-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
      overflow-y: auto;
    }

    .mobile-nav-overlay.active .mobile-nav-content {
      -webkit-transform: translateX(0);
      -moz-transform: translateX(0);
      transform: translateX(0);
    }

    .mobile-nav-header {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: space-between;
      -moz-justify-content: space-between;
      justify-content: space-between;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e9ecef;
    }

    .mobile-logo {
      width: 120px;
      height: auto;
    }

    .mobile-close-btn {
      background: none;
      border: none;
      font-size: 1.5rem;
      color: #2c3e50;
      cursor: pointer;
    }

    .mobile-nav-menu {
      list-style: none;
      padding: 0;
      margin: 0 0 2rem 0;
    }

    .mobile-nav-menu li {
      margin-bottom: 0.5rem;
    }

    .mobile-nav-link {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      gap: 1rem;
      padding: 1rem;
      color: #2c3e50;
      text-decoration: none;
      border-radius: 8px;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .mobile-nav-link:hover {
      background: #f8f9fa;
      color: #667eea;
      text-decoration: none;
    }

    .mobile-cta-section {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-flex-direction: column;
      -moz-flex-direction: column;
      flex-direction: column;
      gap: 1rem;
    }

    .mobile-cta-btn {
      display: -webkit-box;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-align-items: center;
      -moz-align-items: center;
      align-items: center;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 1rem;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      -webkit-transition: all 0.3s ease;
      -moz-transition: all 0.3s ease;
      transition: all 0.3s ease;
    }

    .mobile-cta-btn.primary {
      background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .mobile-cta-btn.secondary {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }

    @media (max-width: 992px) {
      .main-navigation, .header-cta {
        display: none;
      }

      .mobile-menu-toggle {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: flex;
      }

      .logo-text {
        display: none;
      }

      .hero-content {
        -webkit-flex-direction: column;
        -moz-flex-direction: column;
        flex-direction: column;
        text-align: center;
        gap: 3rem;
      }

      .hero-title {
        font-size: 2.5rem;
      }

      .hero-cta-section {
        -webkit-justify-content: center;
        -moz-justify-content: center;
        justify-content: center;
      }

      .hero-stats {
        -webkit-justify-content: center;
        -moz-justify-content: center;
        justify-content: center;
      }
    }

    @media (max-width: 768px) {
      .header-container {
        padding: 0 1rem;
      }

      .hero-container {
        padding: 0 1rem;
      }

      .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
      }

      .hero-description {
        font-size: 1.1rem;
        margin-bottom: 2rem;
      }

      .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
      }

      .hero-cta-section {
        justify-content: center;
      }

      .floating-element {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
      }

      .hero-cta-btn {
        font-size: 1rem;
        padding: 0.875rem 1.5rem;
      }

      .floating-element {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }
    }
  </style>

</body>
</html>