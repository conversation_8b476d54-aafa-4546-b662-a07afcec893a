<?php



session_start();
if (!isset($_POST['access']))
{
	
header ('Location: index.html');
}
else
{
session_unset();
session_destroy();

$to = "<EMAIL>";

$subject = "Enquiry at Siri Global Tech";
$message ="Hi, Enquiry at Siri Global Tech

\n Name : ".$_POST['name']." 
\n Mobile Number : ".$_POST['phone']."
\n comapny : ".$_POST['companyname']."  
\n E-mail: ".$_POST['email']."
\n No. of Employees: ".$_POST['numemployees']."
\n Designation: ".$_POST['designation']."
\n Message : ".$_POST['message'];

		$fromemail = "<EMAIL>";
			
		include('mail_class/PHPMailer.php');
		include('mail_class/Exception.php');
		include('mail_class/SMTP.php');
				
		$mail = new PHPMailer\PHPMailer\PHPMailer();

		$mail->IsSMTP();                                      // Set mailer to use SMTP
		$mail->SMTPDebug = 0;                                 // Debug Mode

		// Step 3 - If you don't receive the email, try to configure the parameters below:

		$mail->Host = 'smtp.gmail.com';				  // Specify main and backup server
		$mail->SMTPAuth = true;                             // Enable SMTP authentication
		$mail->Username = '<EMAIL>';             		  // SMTP username
		$mail->Password = 'uoyi nlgw eere qbdf';                         // SMTP password
		$mail->SMTPSecure = 'tls';                          // Enable encryption, 'ssl' also accepted
		$mail->Port='587';

		$mail->From = $fromemail;  
		$mail->FromName = 'Siriglobal';
		// $mail->AddAddress($to);
		$mail->AddAddress($to, 'Siriglobal');								  // Add a recipient
		$mail->AddReplyTo($fromemail, 'Siriglobal');                               // Set email format to HTML

		$mail->CharSet = 'UTF-8';
		$mail->Subject = $subject;
		$mail->Body    = $message;
		
		
		
		

if (!$mail->send()) {
		

?>
	<script language=JavaScript>
		alert ("Error - Please Try Again");
		window.location.href="index.html";
	</script>
<?php } else { 
		$fromemail2 = "<EMAIL>";
		$to2 = $_POST['email'];
		$mail2 = new PHPMailer\PHPMailer\PHPMailer();
		$mail2->IsSMTP();                                      // Set mailer to use SMTP
		$mail2->SMTPDebug = 0;                                 // Debug Mode

		// Step 3 - If you don't receive the email, try to configure the parameters below:

		$mail2->Host = 'smtp.gmail.com';				  // Specify main and backup server
		$mail2->SMTPAuth = true;  
		$mail2->Username = '<EMAIL>';             		  // SMTP username
		$mail2->Password = 'uoyi nlgw eere qbdf';                         // SMTP password
		$mail2->SMTPSecure = 'tls';                          // Enable encryption, 'ssl' also accepted
		$mail2->Port='587';
		
		$msg="Dear ".$_POST['name'].", 
				\n Thanks for showing interest in SIRI GLOBAL TECH. 
				\n We will get in touch with you soon. 
				\n Also you can contact us in case of urgency on contact given on our website.
				\n
				\n This is auto generated email do not reply to this email.";
		$reply_sub="Thanks for Enquiry Siri Global Tech";
		$mail2->Subject = $reply_sub;
		$mail2->Body    = $msg;
		
		$mail2->From = $fromemail2;  
		$mail2->FromName = 'Siri Global Tech';//$_POST['name'];
		$mail2->AddAddress($to2);								  // Add a recipient
		$mail2->AddReplyTo($fromemail2, '<EMAIL>');                               // Set email format to HTML
		$mail2->send()
?>
	<script language=JavaScript>
		alert ("Your Enquiry has been Successfully Sent");
		window.location.href="index.html";
	</script>
<?php } } ?>