/**
 * Web Vitals monitoring for siriglobaltech.in
 * Tracks Core Web Vitals and sends data to analytics
 */

// Core Web Vitals tracking
class WebVitalsTracker {
  constructor() {
    this.vitals = {};
    this.init();
  }

  init() {
    // Only track in production or when explicitly enabled
    if (this.shouldTrack()) {
      this.trackLCP();
      this.trackFID();
      this.trackCLS();
      this.trackFCP();
      this.trackTTFB();
    }
  }

  shouldTrack() {
    return window.location.hostname !== 'localhost' && 
           window.location.hostname !== '127.0.0.1' ||
           localStorage.getItem('webvitals-debug') === 'true';
  }

  // Largest Contentful Paint
  trackLCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        this.vitals.lcp = {
          value: lastEntry.startTime,
          element: lastEntry.element?.tagName || 'unknown',
          url: lastEntry.url || 'unknown'
        };
        
        this.reportVital('LCP', this.vitals.lcp.value);
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
  }

  // First Input Delay
  trackFID() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const fidValue = entry.processingStart - entry.startTime;
          
          this.vitals.fid = {
            value: fidValue,
            target: entry.target?.tagName || 'unknown'
          };
          
          this.reportVital('FID', fidValue);
        }
      });
      
      observer.observe({ entryTypes: ['first-input'] });
    }
  }

  // Cumulative Layout Shift
  trackCLS() {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      let sessionValue = 0;
      let sessionEntries = [];
      
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            const firstSessionEntry = sessionEntries[0];
            const lastSessionEntry = sessionEntries[sessionEntries.length - 1];
            
            if (sessionValue && 
                entry.startTime - lastSessionEntry.startTime < 1000 &&
                entry.startTime - firstSessionEntry.startTime < 5000) {
              sessionValue += entry.value;
              sessionEntries.push(entry);
            } else {
              sessionValue = entry.value;
              sessionEntries = [entry];
            }
            
            if (sessionValue > clsValue) {
              clsValue = sessionValue;
              this.vitals.cls = {
                value: clsValue,
                entries: sessionEntries.map(e => ({
                  element: e.sources?.[0]?.node?.tagName || 'unknown',
                  value: e.value
                }))
              };
              
              this.reportVital('CLS', clsValue);
            }
          }
        }
      });
      
      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  // First Contentful Paint
  trackFCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.vitals.fcp = {
              value: entry.startTime
            };
            
            this.reportVital('FCP', entry.startTime);
          }
        }
      });
      
      observer.observe({ entryTypes: ['paint'] });
    }
  }

  // Time to First Byte
  trackTTFB() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.entryType === 'navigation') {
            const ttfb = entry.responseStart - entry.requestStart;
            
            this.vitals.ttfb = {
              value: ttfb
            };
            
            this.reportVital('TTFB', ttfb);
          }
        }
      });
      
      observer.observe({ entryTypes: ['navigation'] });
    }
  }

  // Report vital to analytics
  reportVital(name, value) {
    const rating = this.getRating(name, value);
    
    // Log to console in debug mode
    if (localStorage.getItem('webvitals-debug') === 'true') {
      console.log(`${name}: ${value.toFixed(2)}ms (${rating})`);
    }
    
    // Send to Google Analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', name, {
        event_category: 'Web Vitals',
        event_label: rating,
        value: Math.round(value),
        non_interaction: true
      });
    }
    
    // Send to custom analytics endpoint
    this.sendToAnalytics(name, value, rating);
  }

  // Get performance rating
  getRating(name, value) {
    const thresholds = {
      'LCP': { good: 2500, poor: 4000 },
      'FID': { good: 100, poor: 300 },
      'CLS': { good: 0.1, poor: 0.25 },
      'FCP': { good: 1800, poor: 3000 },
      'TTFB': { good: 800, poor: 1800 }
    };
    
    const threshold = thresholds[name];
    if (!threshold) return 'unknown';
    
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  // Send data to custom analytics
  sendToAnalytics(name, value, rating) {
    // Only send in production
    if (window.location.hostname === 'siriglobaltech.in') {
      fetch('/api/analytics/web-vitals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric: name,
          value: value,
          rating: rating,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })
      }).catch(error => {
        console.warn('Failed to send analytics:', error);
      });
    }
  }

  // Get all vitals data
  getVitals() {
    return this.vitals;
  }

  // Export data for debugging
  exportData() {
    const data = {
      vitals: this.vitals,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null
    };
    
    console.log('Web Vitals Data:', data);
    return data;
  }
}

// Initialize tracker
const webVitalsTracker = new WebVitalsTracker();

// Expose for debugging
window.webVitalsTracker = webVitalsTracker;

// Add debug commands
if (localStorage.getItem('webvitals-debug') === 'true') {
  console.log('Web Vitals debugging enabled');
  console.log('Use webVitalsTracker.exportData() to see all metrics');
  console.log('Use webVitalsTracker.getVitals() to see current vitals');
}

// Performance budget alerts
const performanceBudget = {
  LCP: 2500,
  FID: 100,
  CLS: 0.1,
  FCP: 1800,
  TTFB: 800
};

// Check performance budget
function checkPerformanceBudget() {
  const vitals = webVitalsTracker.getVitals();
  const violations = [];
  
  Object.keys(performanceBudget).forEach(metric => {
    const vital = vitals[metric.toLowerCase()];
    if (vital && vital.value > performanceBudget[metric]) {
      violations.push({
        metric,
        value: vital.value,
        budget: performanceBudget[metric],
        overage: vital.value - performanceBudget[metric]
      });
    }
  });
  
  if (violations.length > 0 && localStorage.getItem('webvitals-debug') === 'true') {
    console.warn('Performance budget violations:', violations);
  }
  
  return violations;
}

// Check budget after page load
window.addEventListener('load', () => {
  setTimeout(checkPerformanceBudget, 5000);
});

export { WebVitalsTracker, webVitalsTracker, checkPerformanceBudget };
