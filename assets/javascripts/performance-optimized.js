/**
 * Performance-optimized JavaScript for siriglobaltech.in
 * Combines inline scripts and adds performance improvements
 */

// Mobile Navigation Handler
function initMobileNavigation() {
  const navbar = document.getElementById('navbar');
  const mobileNavShow = document.querySelector('.mobile-nav-show');
  const mobileNavHide = document.querySelector('.mobile-nav-hide');

  if (navbar && mobileNavShow && mobileNavHide) {
    const toggleMobileNav = (isOpen) => {
      if (isOpen) {
        navbar.classList.add('navbar-mobile');
        document.body.style.overflow = 'hidden'; // Prevent background scroll
      } else {
        navbar.classList.remove('navbar-mobile');
        document.body.style.overflow = '';
      }
      mobileNavShow.setAttribute('aria-expanded', isOpen);
      mobileNavHide.setAttribute('aria-expanded', isOpen);
    };

    mobileNavShow.addEventListener('click', () => toggleMobileNav(true));
    mobileNavHide.addEventListener('click', () => toggleMobileNav(false));

    // Close mobile nav when clicking on a link
    navbar.querySelectorAll('a').forEach(link => {
      link.addEventListener('click', () => toggleMobileNav(false));
    });

    // Close mobile nav on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && navbar.classList.contains('navbar-mobile')) {
        toggleMobileNav(false);
      }
    });

    // Initial state
    toggleMobileNav(navbar.classList.contains('navbar-mobile'));
  }
}

// FAQ Toggle Handler with improved performance
function initFAQSection() {
  const faqContainer = document.querySelector('.faq-container');
  
  if (faqContainer) {
    // Use event delegation for better performance
    faqContainer.addEventListener('click', (e) => {
      const faqQuestion = e.target.closest('.faq-question');
      if (!faqQuestion) return;

      const faqItem = faqQuestion.parentElement;
      const wasActive = faqItem.classList.contains('active');
      
      // Close all FAQs with animation
      document.querySelectorAll('.faq-item.active').forEach(item => {
        item.classList.remove('active');
      });
      
      // Open clicked FAQ if it wasn't active
      if (!wasActive) {
        faqItem.classList.add('active');
      }
    });
  }
}

// Lazy loading for images below the fold
function initLazyLoading() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
          }
          img.classList.add('loaded');
          observer.unobserve(img);
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    });

    // Observe all images with data-src attribute
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
}

// Smooth scroll with performance optimization
function initSmoothScroll() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      
      if (target) {
        // Use requestAnimationFrame for smooth performance
        const targetPosition = target.offsetTop - 80; // Account for fixed header
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });
}

// Performance monitoring
function initPerformanceMonitoring() {
  // Monitor Core Web Vitals
  if ('PerformanceObserver' in window) {
    // LCP (Largest Contentful Paint)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        console.log('LCP:', entry.startTime);
      }
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // FID (First Input Delay) / INP (Interaction to Next Paint)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        console.log('FID:', entry.processingStart - entry.startTime);
      }
    }).observe({ entryTypes: ['first-input'] });

    // CLS (Cumulative Layout Shift)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          console.log('CLS:', entry.value);
        }
      }
    }).observe({ entryTypes: ['layout-shift'] });
  }
}

// Preload critical resources on user interaction
function initResourcePreloading() {
  let preloaded = false;
  
  const preloadResources = () => {
    if (preloaded) return;
    preloaded = true;
    
    // Preload non-critical CSS
    const cssFiles = [
      'assets/vendor/aos/aos.css',
      'assets/vendor/glightbox/css/glightbox.min.css',
      'assets/vendor/swiper/swiper-bundle.min.css'
    ];
    
    cssFiles.forEach(href => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      link.onload = () => {
        link.rel = 'stylesheet';
      };
      document.head.appendChild(link);
    });
  };
  
  // Preload on first user interaction
  ['mousedown', 'touchstart', 'keydown'].forEach(event => {
    document.addEventListener(event, preloadResources, { once: true, passive: true });
  });
}

// Initialize all optimizations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  initMobileNavigation();
  initFAQSection();
  initLazyLoading();
  initSmoothScroll();
  initResourcePreloading();
  
  // Initialize performance monitoring in development
  if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    initPerformanceMonitoring();
  }
});

// Service Worker registration for caching
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}
