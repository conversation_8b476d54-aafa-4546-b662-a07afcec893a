<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">

  <title>Contact Siri Global Tech | Freight Software Demo Mumbai</title>

  <meta name="description" content="Get personalized demo of our freight forwarding software. Contact our Mumbai team for logistics ERP solutions. Call +91 98675 95974 today!">
  <meta name="keywords" content="contact freight forwarding software, Mumbai logistics software demo, freight software support, logistics ERP contact, Siri Global Tech Mumbai office">
  
  <meta property="og:locale" content="en_US" />
  <meta property="og:type" content="website" />
  <meta property="og:title" content="Contact Siri Global Tech - Freight Software Demo Mumbai">
  <meta property="og:description" content="Get personalized demo of our freight forwarding software. Contact our Mumbai team for logistics ERP solutions.">
  <meta property="og:url" content="https://www.siriglobaltech.in/contact.html" />
  <meta property="og:site_name" content="siriglobaltech" />

  <meta name="robots" content="index, follow">
  <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  
  <meta name="author" content="Siri Global Tech">
  <meta name="geo.region" content="IN-MH">
  <meta name="geo.placename" content="Mumbai">
  <meta name="geo.position" content="19.0760;72.8777">
  <meta name="ICBM" content="19.0760, 72.8777">

  <link href="https://www.siriglobaltech.in/contact.html" rel="canonical" />
  
  <link rel="alternate" hreflang="en-IN" href="https://www.siriglobaltech.in/contact.html">
  <link rel="alternate" hreflang="en" href="https://www.siriglobaltech.in/contact.html">

  <!-- Resource hints -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  
  <!-- Load CSS asynchronously -->
  <link rel="preload" href="assets/stylesheets/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="assets/stylesheets/bootstrap.min.css"></noscript>
  
  <link rel="preload" href="assets/stylesheets/styles.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="assets/stylesheets/styles.min.css"></noscript>

  <!-- Critical CSS -->
  <style>
    body {
      font-family: 'Poppins', sans-serif;
      color: #222;
      background: #fff;
      margin: 0;
      padding: 0;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 15px;
    }
    
    .hero-section {
      background: linear-gradient(135deg, #33908f 0%, #2c7a79 100%);
      color: white;
      padding: 4rem 0;
      text-align: center;
    }
    
    .hero-title {
      font-size: clamp(2rem, 5vw, 3.5rem);
      font-weight: 700;
      margin-bottom: 1rem;
    }
    
    .hero-subtitle {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
    
    .contact-section {
      padding: 4rem 0;
    }
    
    .contact-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      margin-top: 2rem;
    }
    
    .contact-form {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 10px;
    }
    
    .contact-info {
      padding: 2rem;
    }
    
    .form-group {
      margin-bottom: 1.5rem;
    }
    
    .form-control {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 1rem;
    }
    
    .btn-submit {
      background: #FF5D20;
      color: white;
      padding: 12px 30px;
      border: none;
      border-radius: 30px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .btn-submit:hover {
      background: #e54a1a;
      transform: translateY(-2px);
    }
    
    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .contact-icon {
      font-size: 1.5rem;
      color: #33908f;
      margin-right: 1rem;
      width: 40px;
    }
    
    .breadcrumb {
      background: #f8f9fa;
      padding: 1rem 0;
    }
    
    .breadcrumb a {
      color: #33908f;
      text-decoration: none;
    }
    
    @media (max-width: 768px) {
      .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }
    }
  </style>

  <link rel="icon" type="image/x-icon" href="assets/images/faviicon/favicon.ico" sizes="48x48">
  
  <!-- Schema Markup -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "Contact Siri Global Tech",
    "description": "Contact page for freight forwarding software demos and support",
    "mainEntity": {
      "@type": "Organization",
      "name": "Siri Global Tech",
      "url": "https://www.siriglobaltech.in",
      "logo": "https://www.siriglobaltech.in/assets/images/SIRI_LOGO.webp",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Unit No-01, Ground Floor, Plot No.60, Shri Swami Samarth CHS, Tarun Bharat Society, Chakala, Andheri East",
        "addressLocality": "Mumbai",
        "addressRegion": "Maharashtra",
        "postalCode": "400099",
        "addressCountry": "IN"
      },
      "contactPoint": [
        {
          "@type": "ContactPoint",
          "telephone": "+91-98675-95974",
          "contactType": "Sales",
          "areaServed": "IN",
          "availableLanguage": ["English", "Hindi"]
        },
        {
          "@type": "ContactPoint",
          "telephone": "+91-90291-32115",
          "contactType": "Support",
          "areaServed": "IN",
          "availableLanguage": ["English", "Hindi"]
        }
      ],
      "email": "<EMAIL>",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "19.0760",
        "longitude": "72.8777"
      }
    }
  }
  </script>
</head>

<body>
  <!-- Breadcrumb -->
  <div class="breadcrumb">
    <div class="container">
      <nav aria-label="breadcrumb">
        <a href="/">Home</a> &gt; <span>Contact Us</span>
      </nav>
    </div>
  </div>

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="container">
      <h1 class="hero-title">Contact Siri Global Tech</h1>
      <p class="hero-subtitle">Get a personalized demo of our freight forwarding software solutions</p>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="contact-section">
    <div class="container">
      <div class="contact-grid">
        <!-- Contact Form -->
        <div class="contact-form">
          <h2>Request a Demo</h2>
          <p>Fill out the form below and our team will contact you within 24 hours.</p>
          
          <form action="enquiry_process.php" method="post">
            <div class="form-group">
              <input type="text" name="name" class="form-control" placeholder="Your Name" required>
            </div>
            <div class="form-group">
              <input type="text" name="companyname" class="form-control" placeholder="Company Name" required>
            </div>
            <div class="form-group">
              <input type="email" name="email" class="form-control" placeholder="Email Address" required>
            </div>
            <div class="form-group">
              <input type="tel" name="phone" class="form-control" placeholder="Phone Number" required>
            </div>
            <div class="form-group">
              <select name="numemployees" class="form-control" required>
                <option value="">Number of Employees</option>
                <option value="1-5">1-5</option>
                <option value="5-25">5-25</option>
                <option value="25-50">25-50</option>
                <option value="50-100">50-100</option>
                <option value="100-500">100-500</option>
                <option value="500+">500+</option>
              </select>
            </div>
            <div class="form-group">
              <select name="designation" class="form-control" required>
                <option value="">Your Designation</option>
                <option value="CEO">CEO</option>
                <option value="Operations Manager">Operations Manager</option>
                <option value="Sales Manager">Sales Manager</option>
                <option value="IT Manager">IT Manager</option>
                <option value="Other">Other</option>
              </select>
            </div>
            <div class="form-group">
              <textarea name="message" class="form-control" rows="4" placeholder="Tell us about your requirements..."></textarea>
            </div>
            <button type="submit" class="btn-submit">Send Message</button>
          </form>
        </div>

        <!-- Contact Information -->
        <div class="contact-info">
          <h2>Get in Touch</h2>
          <p>Ready to transform your freight forwarding operations? Our Mumbai-based team is here to help.</p>
          
          <div class="contact-item">
            <div class="contact-icon">📍</div>
            <div>
              <h4>Office Address</h4>
              <p>Unit No-01, Ground Floor<br>
              Plot No.60, Shri Swami Samarth CHS<br>
              Tarun Bharat Society, Chakala<br>
              Andheri East, Mumbai 400099</p>
            </div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">📞</div>
            <div>
              <h4>Phone Numbers</h4>
              <p>Brijesh: <a href="tel:+919867595974">+91 98675 95974</a><br>
              Abhishek: <a href="tel:+919029132115">+91 90291 32115</a></p>
            </div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">✉️</div>
            <div>
              <h4>Email</h4>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          
          <div class="contact-item">
            <div class="contact-icon">🕒</div>
            <div>
              <h4>Business Hours</h4>
              <p>Monday - Friday: 9:00 AM - 6:00 PM<br>
              Saturday: 9:00 AM - 1:00 PM<br>
              Sunday: Closed</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services CTA -->
  <section style="background: #f8f9fa; padding: 3rem 0; text-align: center;">
    <div class="container">
      <h2>Explore Our Software Solutions</h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;">
        <a href="/ocean-freight-software.html" style="background: #33908f; color: white; padding: 1rem; text-decoration: none; border-radius: 5px;">Ocean Freight Software</a>
        <a href="/air-freight-software.html" style="background: #1e40af; color: white; padding: 1rem; text-decoration: none; border-radius: 5px;">Air Freight Software</a>
        <a href="/customs-management.html" style="background: #059669; color: white; padding: 1rem; text-decoration: none; border-radius: 5px;">Customs Management</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer style="background: #222; color: #fff; text-align: center; padding: 2rem 0;">
    <div class="container">
      <p><a href="/" style="color: #33908f;">SIRI GLOBAL TECH</a> © 2025 All Right Reserved | <a href="/terms-and-conditions.html" style="color: #33908f;">Terms & Conditions</a></p>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="assets/javascripts/bootstrap.min.js" defer></script>
</body>

</html>
